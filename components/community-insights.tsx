"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown,
  Users, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  BarChart3,
  Calendar,
  MapPin,
  Award,
  Target,
  Zap,
  ArrowRight
} from 'lucide-react';

interface Stat {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
  description: string;
}

interface Achievement {
  title: string;
  description: string;
  date: string;
  icon: React.ReactNode;
  color: string;
}

const communityStats: Stat[] = [
  {
    label: 'Active Issues',
    value: '34',
    change: '+5 today',
    trend: 'up',
    icon: <AlertTriangle className="h-6 w-6" />,
    description: 'Community reports across Uganda requiring attention'
  },
  {
    label: 'Resolved This Week',
    value: '89',
    change: '+18 from last week',
    trend: 'up',
    icon: <CheckCircle className="h-6 w-6" />,
    description: 'Issues successfully addressed by local councils'
  },
  {
    label: 'Average Response Time',
    value: '3.2h',
    change: '-28% improvement',
    trend: 'down',
    icon: <Clock className="h-6 w-6" />,
    description: 'Time from report to first response'
  },
  {
    label: 'Community Members',
    value: '18,567',
    change: '+234 this month',
    trend: 'up',
    icon: <Users className="h-6 w-6" />,
    description: 'Active Ugandan citizens on our platform'
  },
  {
    label: 'Satisfaction Rate',
    value: '91%',
    change: '+3% this quarter',
    trend: 'up',
    icon: <Target className="h-6 w-6" />,
    description: 'Citizens satisfied with resolution'
  },
  {
    label: 'Response Efficiency',
    value: '83%',
    change: '+12% improvement',
    trend: 'up',
    icon: <Zap className="h-6 w-6" />,
    description: 'Issues resolved within target time'
  }
];

const recentAchievements: Achievement[] = [
  {
    title: 'Record Response Time in Kampala',
    description: 'KCCA completed emergency road repair on Bombo Road in just 2 hours',
    date: 'Yesterday',
    icon: <Award className="h-5 w-5" />,
    color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
  },
  {
    title: 'National Milestone Reached',
    description: '25,000th community issue successfully resolved across Uganda',
    date: '3 days ago',
    icon: <Target className="h-5 w-5" />,
    color: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
  },
  {
    title: 'Highest Participation Month',
    description: 'Record 2,800+ active reports from citizens across all regions',
    date: '1 week ago',
    icon: <TrendingUp className="h-5 w-5" />,
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
  }
];

export function CommunityInsights() {
  const [animatedStats, setAnimatedStats] = useState<boolean[]>(new Array(communityStats.length).fill(false));

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedStats(new Array(communityStats.length).fill(true));
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <BarChart3 className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: 'up' | 'down' | 'stable', isImprovement: boolean = true) => {
    if (trend === 'stable') return 'text-gray-600';
    
    const isPositive = (trend === 'up' && isImprovement) || (trend === 'down' && !isImprovement);
    return isPositive ? 'text-green-600' : 'text-red-600';
  };

  return (
    <section className="py-16 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-black dark:text-white font-serif mb-4">
            Community Insights
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto font-sans">
            Real-time data and analytics showing how our community is working together to solve problems and improve our neighborhood.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {communityStats.map((stat, index) => (
            <div 
              key={stat.label}
              className={`bg-white dark:bg-black border border-gray-200 dark:border-gray-800 p-6 transition-all duration-500 hover:shadow-lg ${
                animatedStats[index] ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
              }`}
              style={{ transitionDelay: `${index * 100}ms` }}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="text-gray-600 dark:text-gray-400">
                  {stat.icon}
                </div>
                <div className="flex items-center gap-1">
                  {getTrendIcon(stat.trend)}
                </div>
              </div>
              
              <div className="space-y-2">
                <h3 className="text-2xl font-bold text-black dark:text-white font-serif">
                  {stat.value}
                </h3>
                <p className="text-sm font-medium text-black dark:text-white">
                  {stat.label}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {stat.description}
                </p>
                <p className={`text-xs font-medium ${getTrendColor(stat.trend, stat.label !== 'Active Issues')}`}>
                  {stat.change}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Two Column Layout */}
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Recent Achievements */}
          <div>
            <h3 className="text-2xl font-bold text-black dark:text-white font-serif mb-6 flex items-center gap-2">
              <Award className="h-6 w-6 text-yellow-600" />
              Recent Achievements
            </h3>
            <div className="space-y-4">
              {recentAchievements.map((achievement, index) => (
                <div 
                  key={index}
                  className="bg-white dark:bg-black border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    <div className={`p-2 rounded-lg ${achievement.color}`}>
                      {achievement.icon}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-black dark:text-white mb-1">
                        {achievement.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {achievement.description}
                      </p>
                      <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-500">
                        <Calendar className="h-3 w-3" />
                        <span>{achievement.date}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Community Highlights */}
          <div>
            <h3 className="text-2xl font-bold text-black dark:text-white font-serif mb-6 flex items-center gap-2">
              <MapPin className="h-6 w-6 text-blue-600" />
              This Week's Highlights
            </h3>
            
            <div className="space-y-6">
              {/* Progress Chart Placeholder */}
              <div className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 p-6">
                <h4 className="font-bold text-black dark:text-white mb-4">Issue Resolution Trends</h4>
                <div className="space-y-3">
                  {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].map((day, index) => {
                    const percentage = Math.floor(Math.random() * 40) + 60;
                    return (
                      <div key={day} className="flex items-center gap-3">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400 w-16">
                          {day.slice(0, 3)}
                        </span>
                        <div className="flex-1 bg-gray-200 dark:bg-gray-700 h-2 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-blue-600 rounded-full transition-all duration-1000 ease-out"
                            style={{ 
                              width: `${percentage}%`,
                              transitionDelay: `${index * 200}ms`
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400 w-8">
                          {percentage}%
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-black border border-gray-200 dark:border-gray-700 p-6">
                <h4 className="font-bold text-black dark:text-white mb-4">Take Action</h4>
                <div className="space-y-3">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white justify-between">
                    Report New Issue
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" className="w-full border border-gray-300 dark:border-gray-700 justify-between">
                    View All Statistics
                    <BarChart3 className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" className="w-full border border-gray-300 dark:border-gray-700 justify-between">
                    Join Community Discussion
                    <Users className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
