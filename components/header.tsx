                          "use client";

import { useState, useEffect, useCallback, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ThemeToggle } from '@/components/theme-toggle';
import { Logo } from '@/components/logo';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu";
import {
  Menu,
  X,
  Search,
  Bell,
  Home,
  AlertCircle,
  BarChart3,
  Shield,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  LogIn,
  FileText,
  HandHeart,
  Newspaper,
  Megaphone,
  Info,
  Building2,
  BarChart2,
  FileCheck,
  History,
  Settings,
  User,
  HelpCircle,
  Globe,
  Zap,
  TrendingUp
} from 'lucide-react';

interface NavItem {
  name: string;
  href: string;
  isNew?: boolean;
  isButton?: boolean;
  icon?: React.ReactNode;
  badge?: string;
}



export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const [notificationCount, setNotificationCount] = useState(3);

  // Scroll detection effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);



  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsSearchOpen(false);
        setIsMenuOpen(false);
      }
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsSearchOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleSearchSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Handle search logic here
      console.log('Searching for:', searchQuery);
      setIsSearchOpen(false);
    }
  }, [searchQuery]);

  const mainNavItems: NavItem[] = useMemo(() => [
    { name: 'Issues', href: '/issues', icon: <AlertCircle className="h-4 w-4" /> },
    { name: 'Breaking News', href: '/breaking-news', isNew: true, icon: <Newspaper className="h-4 w-4" />, badge: 'New' },
    { name: 'Announcements', href: '/announcements', icon: <Megaphone className="h-4 w-4" /> },
    { name: 'About Us', href: '/about-us', icon: <Info className="h-4 w-4" /> },
  ], []);

  const topBarLinks = useMemo(() => [
    { name: 'Authorities', href: '/authority', icon: <Building2 className="h-3 w-3" /> },
    { name: 'Statistics', href: '#', icon: <BarChart2 className="h-3 w-3" /> },
    { name: 'Community Guidelines', href: '#', icon: <FileCheck className="h-3 w-3" /> },
    { name: 'Help Center', href: '#', icon: <HelpCircle className="h-3 w-3" /> },
  ], []);

  return (
    <header className="font-sans relative">
      {/* Top info bar - Compact NYT Style */}
      <div className="bg-white dark:bg-black border-b border-gray-100 dark:border-gray-900 py-1 text-xs shadow-sm">
        <div className="container mx-auto px-4 flex items-center justify-between">
          {/* Date and Edition Info */}
          <div className="flex items-center gap-4 text-gray-600 dark:text-gray-400">
            <span className="font-medium">{new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</span>
            <span>•</span>
            <span>Today's Paper</span>
          </div>

          {/* Live Stats - Compact */}
          <div className="hidden md:flex items-center gap-4 text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>
              <span>23 Active</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              <span>8 Resolved</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>2.4h Avg</span>
            </div>
          </div>

          {/* Top bar links */}
          <div className="hidden lg:flex items-center gap-4">
            {topBarLinks.map((link) => (
              <a
                key={link.name}
                href={link.href}
                className="text-xs font-medium text-muted-foreground hover:text-foreground transition-all duration-200 flex items-center gap-1.5 hover:underline underline-offset-2 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded px-2 py-1"
              >
                {link.icon}
                {link.name}
              </a>
            ))}
            <div className="flex items-center gap-3 ml-2 border-l border-gray-200 dark:border-gray-700 pl-4">
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-blue-600 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded p-1" aria-label="Follow us on Facebook">
                <Facebook className="h-4 w-4" />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-blue-400 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded p-1" aria-label="Follow us on Twitter">
                <Twitter className="h-4 w-4" />
              </a>
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-pink-600 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded p-1" aria-label="Follow us on Instagram">
                <Instagram className="h-4 w-4" />
              </a>
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="text-muted-foreground hover:text-blue-700 transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded p-1" aria-label="Follow us on LinkedIn">
                <Linkedin className="h-4 w-4" />
              </a>
            </div>
            <div className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="text-muted-foreground hover:text-foreground transition-colors relative"
                aria-label={`Notifications ${notificationCount > 0 ? `(${notificationCount} unread)` : ''}`}
              >
                <Bell className="h-4 w-4" />
                {notificationCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
                    {notificationCount > 9 ? '9+' : notificationCount}
                  </span>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main header - NYT Style */}
      <div className={`sticky top-0 z-40 bg-white/98 dark:bg-black/98 backdrop-blur-sm transition-all duration-300 ${
        isScrolled ? 'shadow-lg border-b border-gray-100 dark:border-gray-900' : 'border-b border-gray-50 dark:border-gray-950'
      }`}>
        <div className="container mx-auto px-4 flex h-14 items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
              aria-expanded={isMenuOpen}
            >
              {isMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </Button>
            <a href="/" className="flex items-center gap-3 group">
              <div className="transition-transform group-hover:scale-105">
                <Logo />
              </div>
              <div className="text-left">
                <h1 className="text-2xl font-bold font-serif text-black dark:text-white tracking-tight">
                  The Notice Board
                </h1>
                <p className="text-xs text-gray-600 dark:text-gray-400 font-sans">
                  Community Reports & Democracy
                </p>
              </div>
            </a>
          </div>

          {/* Desktop Navigation - NYT Style */}
          <nav className="hidden md:flex items-center gap-1" role="navigation" aria-label="Main navigation">
            {mainNavItems.map((item) => (
              <Button
                key={item.name}
                variant="ghost"
                asChild
                className="text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-black dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors px-3 py-2"
              >
                <a href={item.href} className="flex items-center gap-2 relative group">
                  <span className="text-gray-600 dark:text-gray-400">{item.icon}</span>
                  <span className="font-sans">{item.name}</span>
                  {item.isNew && (
                    <span className="h-1.5 w-1.5 bg-red-500 rounded-full"></span>
                  )}
                  {item.badge && (
                    <span className="ml-1 px-1.5 py-0.5 text-xs bg-red-600 text-white rounded font-medium">
                      {item.badge}
                    </span>
                  )}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-black dark:bg-white group-hover:w-full transition-all duration-200"></span>
                </a>
              </Button>
            ))}
          </nav>

          {/* Right: User actions */}
          <div className="hidden md:flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="text-muted-foreground hover:text-foreground transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 relative"
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              aria-label="Search (Ctrl+K)"
              title="Search (Ctrl+K)"
            >
              <Search className="h-5 w-5" />
            </Button>

            <ThemeToggle />

            {/* User Menu Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-muted-foreground hover:text-foreground transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  aria-label="User menu"
                >
                  <User className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <LogIn className="mr-2 h-4 w-4" />
                  <span>Log In</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <HelpCircle className="mr-2 h-4 w-4" />
                  <span>Help & Support</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              size="sm"
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white transition-all duration-200 font-medium flex items-center gap-1.5 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 shadow-lg hover:shadow-xl"
            >
              <FileText className="h-4 w-4" />
              Report Issue
            </Button>

            <Button
              size="sm"
              variant="outline"
              className="border-green-100 dark:border-green-800 text-green-700 dark:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 hover:border-green-200 dark:hover:border-green-700 transition-all duration-200 font-medium flex items-center gap-1.5 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 shadow-sm hover:shadow-md"
            >
              <HandHeart className="h-4 w-4" />
              Contribute
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Search Overlay */}
      {isSearchOpen && (
        <div className="fixed inset-0 bg-black/30 backdrop-blur-md z-50 flex items-start justify-center pt-16 animate-fade-in" onClick={() => setIsSearchOpen(false)}>
          <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-4xl mx-4 overflow-hidden animate-scale-in" onClick={(e) => e.stopPropagation()}>
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <Search className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  Search Community Issues
                </h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsSearchOpen(false)}
                  className="hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  aria-label="Close search"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              <form onSubmit={handleSearchSubmit} className="relative mb-8">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Search className="h-6 w-6 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search community issues, locations, or keywords..."
                  className="w-full pl-12 pr-4 py-4 text-lg border border-gray-100 dark:border-gray-800 rounded-xl bg-background focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-300 dark:focus:ring-blue-400 dark:focus:border-blue-600 shadow-lg hover:shadow-xl focus:shadow-xl transition-all duration-200"
                  autoFocus
                />
                <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                  <kbd className="px-2 py-1 text-xs bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 shadow-sm">
                    Enter
                  </kbd>
                </div>
              </form>

              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-sm text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    Popular searches
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {['Street lights', 'Potholes', 'Noise complaints', 'Graffiti', 'Park maintenance', 'Water issues'].map((term) => (
                      <button
                        key={term}
                        onClick={() => setSearchQuery(term)}
                        className="px-4 py-2 bg-gradient-to-r from-gray-100 to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-full hover:from-blue-50 hover:to-blue-100 dark:hover:from-blue-900/20 dark:hover:to-blue-800/20 transition-all duration-200 text-sm font-medium flex items-center gap-2 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                      >
                        <Search className="h-3 w-3" />
                        {term}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-sm text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                    <History className="h-4 w-4 text-green-600 dark:text-green-400" />
                    Recent searches
                  </h3>
                  <div className="space-y-2">
                    {['Road construction updates', 'Community center events', 'Recycling schedule', 'Public transportation'].map((term) => (
                      <button
                        key={term}
                        onClick={() => setSearchQuery(term)}
                        className="w-full px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 text-sm flex items-center gap-3 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                      >
                        <History className="h-4 w-4 text-gray-400" />
                        <span className="flex-1">{term}</span>
                        <Search className="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Quick filters */}
              <div className="mt-6 pt-6 border-t border-gray-100 dark:border-gray-800">
                <h3 className="font-semibold text-sm text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                  <Globe className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  Quick filters
                </h3>
                <div className="flex flex-wrap gap-2">
                  {[
                    { label: 'Open Issues', color: 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-300' },
                    { label: 'In Progress', color: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300' },
                    { label: 'Resolved', color: 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300' },
                    { label: 'High Priority', color: 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300' }
                  ].map((filter) => (
                    <button
                      key={filter.label}
                      className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 ${filter.color}`}
                    >
                      {filter.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/50 px-6 py-4 flex justify-between items-center border-t border-gray-100 dark:border-gray-800">
              <div className="flex items-center gap-4 text-xs text-gray-500">
                <span className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 shadow-sm">ESC</kbd>
                  to close
                </span>
                <span className="flex items-center gap-1">
                  <kbd className="px-1.5 py-0.5 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700 shadow-sm">↑↓</kbd>
                  to navigate
                </span>
              </div>
              <Button size="sm" variant="outline" className="text-xs hover:scale-105 transition-transform">
                Advanced Search
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Mobile menu overlay */}
      {isMenuOpen && (
        <div className="md:hidden bg-background/95 backdrop-blur-md animate-slide-down pb-20 shadow-lg border-t border-gray-100 dark:border-gray-900">
          <nav className="container mx-auto px-4 py-6" role="navigation" aria-label="Mobile navigation">
            {/* Search bar for mobile */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search issues..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-100 dark:border-gray-800 rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-300 dark:focus:border-blue-600 shadow-sm hover:shadow-md transition-all duration-200"
                  onClick={() => {
                    setIsMenuOpen(false);
                    setIsSearchOpen(true);
                  }}
                />
              </div>
            </div>

            {/* Navigation links */}
            <div className="grid grid-cols-2 gap-3 mb-6">
              {[
                { name: 'Report Issue', href: '/report', icon: <FileText className="h-4 w-4" />, primary: true },
                { name: 'Issues', href: '/', icon: <AlertCircle className="h-4 w-4" /> },
                { name: 'Authorities', href: '/authority', icon: <Building2 className="h-4 w-4" /> },
                { name: 'Statistics', href: '#', icon: <BarChart2 className="h-4 w-4" /> },
                { name: 'Announcements', href: '#', icon: <Megaphone className="h-4 w-4" /> },
                { name: 'About', href: '#', icon: <Info className="h-4 w-4" /> }
              ].map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className={`text-sm font-medium py-4 px-4 rounded-xl transition-all duration-200 text-center hover:scale-105 hover:shadow-lg flex flex-col items-center gap-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 ${
                    item.primary
                      ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-md hover:from-blue-700 hover:to-blue-800 hover:shadow-lg'
                      : 'border border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 hover:border-gray-200 dark:hover:border-gray-700 shadow-sm hover:shadow-md'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className={item.primary ? 'text-white' : 'text-blue-600 dark:text-blue-400'}>
                    {item.icon}
                  </span>
                  {item.name}
                </a>
              ))}
            </div>

            {/* Quick actions */}
            <div className="space-y-3 mb-6">
              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                Quick Actions
              </h3>
              <div className="grid grid-cols-2 gap-2">
                <button className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-all duration-200 text-sm font-medium">
                  <HandHeart className="h-4 w-4" />
                  Contribute
                </button>
                <button className="flex items-center gap-2 p-3 bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-all duration-200 text-sm font-medium">
                  <Bell className="h-4 w-4" />
                  Notifications
                </button>
              </div>
            </div>

            {/* User actions for mobile */}
            <div className="flex items-center justify-center gap-4 pt-4 border-t border-gray-100 dark:border-gray-800">
              <Button variant="outline" size="sm" className="flex-1 hover:scale-105 transition-transform">
                <User className="h-4 w-4 mr-2" />
                Log In
              </Button>
              <Button variant="ghost" size="sm" className="flex-1 hover:scale-105 transition-transform">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </nav>
        </div>
      )}

      {/* Mobile Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-t border-gray-100 dark:border-gray-800 md:hidden z-50 shadow-2xl">
        <div className="grid grid-cols-5 gap-0">
          <a
            href="/"
            className="flex flex-col items-center justify-center py-3 px-2 text-xs text-muted-foreground hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 relative group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            <Home className="h-5 w-5 mb-1 transition-transform group-hover:scale-110" />
            <span className="font-medium">Home</span>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-blue-600 group-hover:w-8 transition-all duration-300"></div>
          </a>
          <a
            href="/report"
            className="flex flex-col items-center justify-center py-3 px-2 text-xs text-muted-foreground hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 relative group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            <div className="relative">
              <AlertCircle className="h-5 w-5 mb-1 transition-transform group-hover:scale-110" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-bold">!</span>
              </div>
            </div>
            <span className="font-medium">Report</span>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-blue-600 group-hover:w-8 transition-all duration-300"></div>
          </a>
          <a
            href="/authority"
            className="flex flex-col items-center justify-center py-3 px-2 text-xs text-muted-foreground hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 relative group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            <Shield className="h-5 w-5 mb-1 transition-transform group-hover:scale-110" />
            <span className="font-medium">Authority</span>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-blue-600 group-hover:w-8 transition-all duration-300"></div>
          </a>
          <button
            className="flex flex-col items-center justify-center py-3 px-2 text-xs text-muted-foreground hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 relative group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            <BarChart3 className="h-5 w-5 mb-1 transition-transform group-hover:scale-110" />
            <span className="font-medium">Stats</span>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-blue-600 group-hover:w-8 transition-all duration-300"></div>
          </button>
          <button
            className="flex flex-col items-center justify-center py-3 px-2 text-xs text-muted-foreground hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200 relative group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
            aria-label={`Notifications ${notificationCount > 0 ? `(${notificationCount} unread)` : ''}`}
          >
            <div className="relative">
              <Bell className="h-5 w-5 mb-1 transition-transform group-hover:scale-110" />
              {notificationCount > 0 && (
                <>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center animate-pulse">
                    <span className="text-white text-xs font-bold">
                      {notificationCount > 9 ? '9+' : notificationCount}
                    </span>
                  </div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-ping opacity-75"></div>
                </>
              )}
            </div>
            <span className="font-medium">Alerts</span>
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-blue-600 group-hover:w-8 transition-all duration-300"></div>
          </button>
        </div>
      </div>
    </header>
  );
}