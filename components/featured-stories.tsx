"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Clock, 
  Eye, 
  MessageSquare, 
  ThumbsUp, 
  ArrowRight,
  TrendingUp,
  AlertTriangle,
  Users,
  MapPin
} from 'lucide-react';

interface Story {
  id: string;
  category: string;
  title: string;
  excerpt: string;
  author: string;
  publishDate: string;
  readTime: string;
  views: string;
  likes: number;
  comments: number;
  imageUrl: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  location?: string;
}

const featuredStories: Story[] = [
  {
    id: '1',
    category: 'INFRASTRUCTURE',
    title: 'NWSC Begins Major Water Supply Upgrade in Entebbe',
    excerpt: 'National Water and Sewerage Corporation launches UGX 12 billion infrastructure project to improve water quality and pressure for over 15,000 residents in Entebbe Municipality.',
    author: '<PERSON>',
    publishDate: '2 hours ago',
    readTime: '4 min read',
    views: '1.8k',
    likes: 42,
    comments: 15,
    imageUrl: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=600&h=400&fit=crop', // Water infrastructure/pipes
    priority: 'high',
    location: 'Entebbe Municipality'
  },
  {
    id: '2',
    category: 'COMMUNITY',
    title: 'Kampala Food Relief Program Sees Record Community Support',
    excerpt: 'Residents of Kampala rally together as local NGOs report 200% increase in volunteer participation, helping serve vulnerable families across the five divisions.',
    author: 'Grace Nakimuli',
    publishDate: '4 hours ago',
    readTime: '3 min read',
    views: '2.1k',
    likes: 67,
    comments: 23,
    imageUrl: 'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?w=600&h=400&fit=crop', // Community food distribution
    priority: 'medium',
    location: 'Kampala Central'
  },
  {
    id: '3',
    category: 'ENVIRONMENT',
    title: 'Fort Portal Waste Management Initiative Exceeds Targets',
    excerpt: 'The innovative community recycling program in Fort Portal has exceeded expectations, diverting over 500 tonnes of waste from Kabarole landfills in just three months.',
    author: 'Peter Tumwebaze',
    publishDate: '6 hours ago',
    readTime: '5 min read',
    views: '1.5k',
    likes: 38,
    comments: 12,
    imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop', // Waste management/recycling
    priority: 'medium',
    location: 'Fort Portal City'
  }
];

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'INFRASTRUCTURE':
      return 'bg-blue-600 text-white';
    case 'COMMUNITY':
      return 'bg-green-600 text-white';
    case 'ENVIRONMENT':
      return 'bg-emerald-600 text-white';
    case 'SAFETY':
      return 'bg-orange-600 text-white';
    case 'BREAKING':
      return 'bg-red-600 text-white';
    default:
      return 'bg-gray-600 text-white';
  }
};

const getPriorityIcon = (priority: string) => {
  switch (priority) {
    case 'critical':
      return <AlertTriangle className="h-4 w-4 text-red-600" />;
    case 'high':
      return <TrendingUp className="h-4 w-4 text-orange-600" />;
    default:
      return null;
  }
};

export function FeaturedStories() {
  const [hoveredStory, setHoveredStory] = useState<string | null>(null);

  return (
    <section className="py-12 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold text-black dark:text-white font-serif mb-2">
              Featured Stories
            </h2>
            <p className="text-gray-600 dark:text-gray-400 font-sans">
              In-depth coverage of the issues that matter most to our community
            </p>
          </div>
          <Button variant="outline" className="border border-gray-300 dark:border-gray-700">
            View All Stories
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>

        {/* Stories Grid */}
        <div className="grid lg:grid-cols-3 gap-8">
          {featuredStories.map((story, index) => (
            <article 
              key={story.id}
              className="group cursor-pointer"
              onMouseEnter={() => setHoveredStory(story.id)}
              onMouseLeave={() => setHoveredStory(null)}
            >
              <div className="border border-gray-200 dark:border-gray-800 bg-white dark:bg-black overflow-hidden hover:shadow-xl transition-all duration-300">
                {/* Story Image */}
                <div className="aspect-[4/3] relative overflow-hidden">
                  <img
                    src={story.imageUrl}
                    alt={story.title}
                    className={`w-full h-full object-cover transition-transform duration-500 ${
                      hoveredStory === story.id ? 'scale-110' : 'scale-100'
                    }`}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://via.placeholder.com/600x400/e5e7eb/6b7280?text=${encodeURIComponent(story.category)}`;
                    }}
                  />
                  
                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <span className={`px-3 py-1 text-xs font-bold tracking-wider ${getCategoryColor(story.category)} shadow-lg`}>
                      {story.category}
                    </span>
                  </div>

                  {/* Priority Indicator */}
                  {getPriorityIcon(story.priority) && (
                    <div className="absolute top-4 right-4 bg-white/90 dark:bg-black/90 rounded-full p-2">
                      {getPriorityIcon(story.priority)}
                    </div>
                  )}

                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>

                {/* Story Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-black dark:text-white mb-3 leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors font-serif">
                    {story.title}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed font-sans">
                    {story.excerpt}
                  </p>

                  {/* Story Meta */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <span className="font-medium">By {story.author}</span>
                      <span>•</span>
                      <span>{story.publishDate}</span>
                      <span>•</span>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{story.readTime}</span>
                      </div>
                    </div>

                    {story.location && (
                      <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                        <MapPin className="h-3 w-3" />
                        <span>{story.location}</span>
                      </div>
                    )}

                    {/* Engagement Stats */}
                    <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
                      <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          <span>{story.views}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <ThumbsUp className="h-4 w-4" />
                          <span>{story.likes}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageSquare className="h-4 w-4" />
                          <span>{story.comments}</span>
                        </div>
                      </div>
                      
                      <Button 
                        variant="ghost" 
                        size="sm"
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 p-0 h-auto font-medium"
                      >
                        Read More →
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
}
