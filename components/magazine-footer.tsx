"use client";

import { Button } from '@/components/ui/button';
import { 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Clock,
  Award,
  Users,
  ArrowRight
} from 'lucide-react';

export function MagazineFooter() {
  const currentYear = new Date().getFullYear();

  const sections = [
    {
      title: "EDITORIAL",
      links: [
        { label: "About Us", href: "/about" },
        { label: "Contact", href: "/contact" },
        { label: "Submit Story", href: "/submit" },
        { label: "Press", href: "/press" }
      ]
    },
    {
      title: "COMMUNITY",
      links: [
        { label: "Report Issue", href: "/report" },
        { label: "Guidelines", href: "/guidelines" },
        { label: "Authorities", href: "/authority" },
        { label: "Events", href: "/events" }
      ]
    },
    {
      title: "RESOURCES",
      links: [
        { label: "Emergency", href: "/emergency" },
        { label: "Public Records", href: "/records" },
        { label: "City Services", href: "/services" },
        { label: "FAQ", href: "/faq" }
      ]
    }
  ];



  return (
    <footer className="bg-black dark:bg-white text-white dark:text-black border-t border-gray-300 dark:border-gray-700">
      {/* Compact Main Footer Content */}
      <div className="border-b border-gray-800 dark:border-gray-200 py-8">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-5 gap-6">
            {/* Compact Brand Section */}
            <div className="lg:col-span-2">
              <h3 className="text-xl font-bold mb-3 font-serif">
                THE NOTICE BOARD
              </h3>
              <p className="text-gray-300 dark:text-gray-700 mb-4 text-sm leading-relaxed">
                Your trusted source for Ugandan community news and civic engagement.
              </p>

              {/* Compact Contact & Social */}
              <div className="flex flex-wrap items-center gap-4 text-xs text-gray-400 dark:text-gray-600">
                <div className="flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-1">
                  <Phone className="h-3 w-3" />
                  <span>+256 700 123 456</span>
                </div>
              </div>

              {/* Social Media */}
              <div className="flex items-center gap-3 mt-3">
                <a href="#" className="text-gray-400 dark:text-gray-600 hover:text-white dark:hover:text-black transition-colors">
                  <Facebook className="h-4 w-4" />
                </a>
                <a href="#" className="text-gray-400 dark:text-gray-600 hover:text-white dark:hover:text-black transition-colors">
                  <Twitter className="h-4 w-4" />
                </a>
                <a href="#" className="text-gray-400 dark:text-gray-600 hover:text-white dark:hover:text-black transition-colors">
                  <Instagram className="h-4 w-4" />
                </a>
                <a href="#" className="text-gray-400 dark:text-gray-600 hover:text-white dark:hover:text-black transition-colors">
                  <Linkedin className="h-4 w-4" />
                </a>
              </div>
            </div>

            {/* Compact Navigation Sections */}
            {sections.slice(0, 3).map((section, index) => (
              <div key={index}>
                <h4 className="text-xs font-bold tracking-wider mb-3 text-gray-400 dark:text-gray-600 uppercase">
                  {section.title}
                </h4>
                <ul className="space-y-1">
                  {section.links.slice(0, 4).map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <a
                        href={link.href}
                        className="text-xs text-gray-300 dark:text-gray-700 hover:text-white dark:hover:text-black transition-colors"
                      >
                        {link.label}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Compact Newsletter Signup */}
          <div className="border-t border-gray-800 dark:border-gray-200 pt-6 mt-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h4 className="text-sm font-bold mb-1">DAILY DIGEST</h4>
                <p className="text-xs text-gray-300 dark:text-gray-700">
                  Get community updates delivered to your inbox.
                </p>
              </div>
              <div className="flex gap-2 max-w-md">
                <input
                  type="email"
                  placeholder="Enter email"
                  className="flex-1 px-3 py-2 bg-white dark:bg-black text-black dark:text-white border border-gray-700 dark:border-gray-300 focus:outline-none focus:border-white dark:focus:border-black text-sm"
                />
                <Button size="sm" className="bg-white dark:bg-black text-black dark:text-white hover:bg-gray-200 dark:hover:bg-gray-800 px-4">
                  Subscribe
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compact Bottom Bar */}
      <div className="py-4">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-3 text-xs">
            <div className="flex items-center gap-4 text-gray-400 dark:text-gray-600">
              <span>© {currentYear} The Notice Board</span>
              <span>•</span>
              <span>Edition #247</span>
              <span>•</span>
              <span>ISSN 2024-0001</span>
            </div>

            <div className="flex items-center gap-4">
              <a href="/privacy" className="text-gray-400 dark:text-gray-600 hover:text-white dark:hover:text-black transition-colors">
                Privacy
              </a>
              <a href="/terms" className="text-gray-400 dark:text-gray-600 hover:text-white dark:hover:text-black transition-colors">
                Terms
              </a>
              <a href="/ethics" className="text-gray-400 dark:text-gray-600 hover:text-white dark:hover:text-black transition-colors">
                Ethics
              </a>
              <a href="/corrections" className="text-gray-400 dark:text-gray-600 hover:text-white dark:hover:text-black transition-colors">
                Corrections
              </a>
            </div>
          </div>

          {/* Compact Publication Info */}
          <div className="mt-3 pt-3 border-t border-gray-800 dark:border-gray-200 text-center">
            <div className="flex flex-wrap items-center justify-center gap-3 text-xs text-gray-500 dark:text-gray-500">
              <div className="flex items-center gap-1">
                <Award className="h-3 w-3" />
                <span>Best Community Journalism 2023</span>
              </div>
              <span>•</span>
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>Serving since 2020</span>
              </div>
              <span>•</span>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>Updated every 15 min</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
