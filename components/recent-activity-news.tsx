"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  MessageSquare, 
  User, 
  MapPin,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Eye,
  ThumbsUp,
  ArrowRight,
  Calendar,
  Filter,
  RefreshCw
} from 'lucide-react';

interface Activity {
  id: string;
  type: 'report' | 'resolution' | 'comment' | 'update';
  title: string;
  description: string;
  user: string;
  location: string;
  timestamp: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  engagement: {
    views: number;
    likes: number;
    comments: number;
  };
  imageUrl?: string;
}

const recentActivities: Activity[] = [
  {
    id: '1',
    type: 'resolution',
    title: 'Streetlight Repair Completed on Jinja Road',
    description: 'The faulty streetlight reported last week has been successfully repaired by KCCA maintenance team.',
    user: 'KCCA Maintenance',
    location: 'Jinja Road, Nakawa',
    timestamp: '2 hours ago',
    engagement: { views: 234, likes: 18, comments: 5 },
    imageUrl: 'https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=300&h=200&fit=crop' // Urban infrastructure
  },
  {
    id: '2',
    type: 'report',
    title: 'Large Pothole Reported on Entebbe Road',
    description: 'Major pothole causing traffic congestion and vehicle damage near Kajjansi trading center.',
    user: 'Sarah Namukasa',
    location: 'Entebbe Road, Kajjansi',
    timestamp: '4 hours ago',
    priority: 'high',
    engagement: { views: 156, likes: 12, comments: 8 },
    imageUrl: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=300&h=200&fit=crop' // Road issues
  },
  {
    id: '3',
    type: 'update',
    title: 'Parish Development Committee Meeting - Thursday',
    description: 'Monthly PDC meeting to discuss community development projects and budget allocation for Rubaga Division.',
    user: 'Rubaga PDC',
    location: 'Rubaga Community Hall',
    timestamp: '6 hours ago',
    engagement: { views: 445, likes: 34, comments: 15 }
  },
  {
    id: '4',
    type: 'comment',
    title: 'Update on Construction Noise Complaint',
    description: 'Building contractor has agreed to limit construction hours to 7 AM - 6 PM on weekdays only.',
    user: 'Moses Kiwanuka',
    location: 'Kololo, Kampala',
    timestamp: '8 hours ago',
    engagement: { views: 89, likes: 7, comments: 3 }
  },
  {
    id: '5',
    type: 'resolution',
    title: 'Cleanup Completed at Centenary Park',
    description: 'Community volunteers successfully removed litter and cleared overgrown vegetation from the park.',
    user: 'Kampala Volunteers',
    location: 'Centenary Park, Kampala',
    timestamp: '12 hours ago',
    engagement: { views: 178, likes: 25, comments: 9 },
    imageUrl: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=300&h=200&fit=crop' // Community cleanup
  },
  {
    id: '6',
    type: 'report',
    title: 'Fallen Tree Blocking Buganda Road',
    description: 'Large tree fell during last night\'s heavy rains, blocking traffic on Buganda Road near Parliament.',
    user: 'Peter Ssemakula',
    location: 'Buganda Road, Central',
    timestamp: '1 day ago',
    priority: 'medium',
    engagement: { views: 67, likes: 4, comments: 2 }
  }
];

const getActivityIcon = (type: Activity['type']) => {
  switch (type) {
    case 'report':
      return <AlertTriangle className="h-5 w-5 text-orange-600" />;
    case 'resolution':
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    case 'comment':
      return <MessageSquare className="h-5 w-5 text-blue-600" />;
    case 'update':
      return <TrendingUp className="h-5 w-5 text-purple-600" />;
    default:
      return <Clock className="h-5 w-5 text-gray-600" />;
  }
};

const getActivityTypeLabel = (type: Activity['type']) => {
  switch (type) {
    case 'report':
      return 'New Report';
    case 'resolution':
      return 'Resolved';
    case 'comment':
      return 'Comment';
    case 'update':
      return 'Update';
    default:
      return 'Activity';
  }
};

export function RecentActivityNews() {
  const [filter, setFilter] = useState('all');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const filteredActivities = filter === 'all' 
    ? recentActivities 
    : recentActivities.filter(activity => activity.type === filter);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  const filterOptions = [
    { value: 'all', label: 'All Activity' },
    { value: 'report', label: 'Reports' },
    { value: 'resolution', label: 'Resolutions' },
    { value: 'update', label: 'Updates' },
    { value: 'comment', label: 'Comments' }
  ];

  return (
    <section className="py-16 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <div className="mb-4 lg:mb-0">
            <h2 className="text-3xl font-bold text-black dark:text-white font-serif mb-2">
              Recent Activity
            </h2>
            <p className="text-gray-600 dark:text-gray-400 font-sans">
              Stay updated with the latest community developments and resolutions
            </p>
          </div>

          <div className="flex items-center gap-3">
            {/* Filter Dropdown */}
            <div className="relative">
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="appearance-none bg-white dark:bg-black border border-gray-300 dark:border-gray-700 rounded px-4 py-2 pr-8 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {filterOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <Filter className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>

            {/* Refresh Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="border border-gray-300 dark:border-gray-700"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Activity Feed */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Activity List */}
          <div className="lg:col-span-2">
            <div className="space-y-6">
              {filteredActivities.map((activity, index) => (
                <article 
                  key={activity.id}
                  className="border border-gray-200 dark:border-gray-800 bg-white dark:bg-black p-6 hover:shadow-lg transition-all duration-300"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="flex gap-4">
                    {/* Activity Image */}
                    {activity.imageUrl && (
                      <div className="flex-shrink-0">
                        <img
                          src={activity.imageUrl}
                          alt={activity.title}
                          className="w-24 h-24 object-cover border border-gray-200 dark:border-gray-700"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                      </div>
                    )}

                    {/* Activity Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          {getActivityIcon(activity.type)}
                          <span className="text-sm font-bold text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                            {getActivityTypeLabel(activity.type)}
                          </span>
                          {activity.priority && (
                            <span className={`px-2 py-1 text-xs font-bold rounded ${
                              activity.priority === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                              activity.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' :
                              activity.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                              'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                            }`}>
                              {activity.priority.toUpperCase()}
                            </span>
                          )}
                        </div>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {activity.timestamp}
                        </span>
                      </div>

                      <h3 className="text-lg font-bold text-black dark:text-white mb-2 font-serif">
                        {activity.title}
                      </h3>
                      
                      <p className="text-gray-600 dark:text-gray-300 mb-3 font-sans">
                        {activity.description}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            <span>{activity.user}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            <span>{activity.location}</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                          <div className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            <span>{activity.engagement.views}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <ThumbsUp className="h-4 w-4" />
                            <span>{activity.engagement.likes}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-4 w-4" />
                            <span>{activity.engagement.comments}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
              ))}
            </div>

            {/* Load More */}
            <div className="text-center mt-8">
              <Button variant="outline" size="lg" className="border border-gray-300 dark:border-gray-700">
                Load More Activity
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Activity Summary */}
            <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-800 p-6">
              <h3 className="text-lg font-bold text-black dark:text-white mb-4 font-serif">
                Activity Summary
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Today</span>
                  <span className="font-bold text-black dark:text-white">12 activities</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">This Week</span>
                  <span className="font-bold text-black dark:text-white">89 activities</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">This Month</span>
                  <span className="font-bold text-black dark:text-white">342 activities</span>
                </div>
              </div>
            </div>

            {/* Trending Topics */}
            <div className="bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-800 p-6">
              <h3 className="text-lg font-bold text-black dark:text-white mb-4 font-serif">
                Trending Topics
              </h3>
              <div className="space-y-2">
                {['Infrastructure', 'Road Maintenance', 'Public Safety', 'Community Events'].map((topic, index) => (
                  <div key={topic} className="flex items-center justify-between p-2 hover:bg-white dark:hover:bg-black transition-colors">
                    <span className="text-sm text-black dark:text-white font-medium">{topic}</span>
                    <span className="text-xs text-gray-500">#{index + 1}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-black dark:bg-white text-white dark:text-black p-6">
              <h3 className="text-lg font-bold mb-2">Stay Engaged</h3>
              <p className="text-sm mb-4 opacity-90">
                Subscribe to notifications and never miss important community updates.
              </p>
              <Button className="w-full bg-white dark:bg-black text-black dark:text-white hover:bg-gray-100 dark:hover:bg-gray-900">
                Subscribe Now
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
