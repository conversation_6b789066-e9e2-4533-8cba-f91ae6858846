"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  AlertCircle,
  MapPin,
  Camera,
  Upload,
  Phone,
  Mail,
  MessageSquare,
  CheckCircle,
  Clock,
  User,
  Building,
  Flag,
  FileText,
  Home,
  ChevronRight,
  Info,
  Shield,
  Zap,
  Heart,
  Star,
  TrendingUp,
  Eye,
  ThumbsUp,
  Share2,
  Bookmark,
  Download,
  HelpCircle,
  Lightbulb,
  Target,
  Award,
  Users,
  Calendar,
  Timer,
  Activity,
  Bell,
  BarChart3,
  Settings,
  Search
} from 'lucide-react';

const issueCategories = [
  { value: "infrastructure", label: "Infrastructure", icon: Building, color: "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300" },
  { value: "roads", label: "Roads & Transportation", icon: MapPin, color: "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300" },
  { value: "utilities", label: "Utilities", icon: Zap, color: "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-300" },
  { value: "parks", label: "Parks & Recreation", icon: Heart, color: "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-300" },
  { value: "safety", label: "Public Safety", icon: Shield, color: "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-300" },
  { value: "noise", label: "Noise Complaints", icon: Activity, color: "bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300" },
  { value: "environment", label: "Environmental", icon: Star, color: "bg-teal-100 text-teal-700 dark:bg-teal-900/20 dark:text-teal-300" },
  { value: "other", label: "Other", icon: FileText, color: "bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-300" }
];

const priorityLevels = [
  { value: "low", label: "Low Priority", description: "Non-urgent, can wait for regular maintenance", color: "bg-green-50 text-green-700 border-green-200" },
  { value: "medium", label: "Medium Priority", description: "Moderate concern, should be addressed soon", color: "bg-yellow-50 text-yellow-700 border-yellow-200" },
  { value: "high", label: "High Priority", description: "Urgent issue requiring immediate attention", color: "bg-red-50 text-red-700 border-red-200" }
];

const recentReports = [
  { title: "Street Light Outage on Main St", status: "pending", time: "2 hours ago", category: "Infrastructure" },
  { title: "Pothole on Park Avenue", status: "in-progress", time: "5 hours ago", category: "Roads" },
  { title: "Graffiti at Central Library", status: "resolved", time: "1 day ago", category: "Safety" },
  { title: "Noise Complaint - Construction", status: "pending", time: "3 hours ago", category: "Noise" }
];

export default function ReportIssuePage() {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    priority: '',
    location: '',
    contactName: '',
    contactEmail: '',
    contactPhone: '',
    anonymous: false
  });
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setShowSuccess(true);
      // Reset form
      setFormData({
        title: '',
        description: '',
        category: '',
        priority: '',
        location: '',
        contactName: '',
        contactEmail: '',
        contactPhone: '',
        anonymous: false
      });
      setSelectedImages([]);
    }, 2000);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    
    const files = Array.from(e.target.files);
    const newFiles = files.filter(file => {
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');
      const isValidSize = file.size <= 50 * 1024 * 1024; // 50MB limit
      
      if (!isValidSize) {
        alert(`File ${file.name} is too large. Maximum size is 50MB.`);
        return false;
      }
      
      return isImage || isVideo;
    });
    
    setSelectedImages(prev => {
      const currentImages = prev.filter(f => f.type.startsWith('image/'));
      const currentVideos = prev.filter(f => f.type.startsWith('video/'));
      const newImages = newFiles.filter(f => f.type.startsWith('image/'));
      const newVideos = newFiles.filter(f => f.type.startsWith('video/'));
      
      // Limit to 3 photos total
      const finalImages = [...currentImages, ...newImages].slice(0, 3);
      // Limit to 1 video total
      const finalVideos = newVideos.length > 0 ? [newVideos[0]] : currentVideos;
      
      return [...finalImages, ...finalVideos];
    });
  };

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20">
        <div className="max-w-4xl mx-auto px-4 py-16">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-green-500 rounded-full mb-6 animate-scale-in">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>
            <h1 className="font-playfair text-4xl font-bold mb-4 animate-fade-in">Report Submitted Successfully!</h1>
            <p className="text-lg text-muted-foreground mb-8 animate-fade-in" style={{animationDelay: '0.2s'}}>
              Thank you for helping improve our community. Your report has been received and assigned ID: <strong>RPT-2024-{Math.floor(Math.random() * 1000)}</strong>
            </p>
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <Card className="animate-fade-in" style={{animationDelay: '0.4s'}}>
                <CardContent className="p-6 text-center">
                  <Timer className="h-8 w-8 text-blue-500 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">What's Next?</h3>
                  <p className="text-sm text-muted-foreground">Your report will be reviewed within 24 hours</p>
                </CardContent>
              </Card>
              <Card className="animate-fade-in" style={{animationDelay: '0.6s'}}>
                <CardContent className="p-6 text-center">
                  <Bell className="h-8 w-8 text-green-500 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Stay Updated</h3>
                  <p className="text-sm text-muted-foreground">You'll receive email notifications on progress</p>
                </CardContent>
              </Card>
              <Card className="animate-fade-in" style={{animationDelay: '0.8s'}}>
                <CardContent className="p-6 text-center">
                  <Users className="h-8 w-8 text-purple-500 mx-auto mb-3" />
                  <h3 className="font-semibold mb-2">Community Impact</h3>
                  <p className="text-sm text-muted-foreground">Your report helps make our community better</p>
                </CardContent>
              </Card>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button onClick={() => setShowSuccess(false)} className="btn-hover">
                Submit Another Report
              </Button>
              <Button variant="outline" onClick={() => window.location.href = '/'} className="btn-hover">
                Return to Home
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header Section */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          {/* Breadcrumb Navigation */}
          <nav className="flex items-center gap-2 text-sm text-muted-foreground mb-4 animate-slide-in">
            <Home className="h-4 w-4" />
            <ChevronRight className="h-3 w-3" />
            <a href="/" className="hover:text-foreground transition-colors">Home</a>
            <ChevronRight className="h-3 w-3" />
            <span className="text-foreground font-medium">Report an Issue</span>
          </nav>

          {/* Page Title and Info */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="animate-fade-in">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <AlertCircle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h1 className="font-playfair text-3xl font-bold">Report a Community Issue</h1>
              </div>
              <p className="text-muted-foreground">Help improve your community by reporting issues that need attention</p>
            </div>
            
            <div className="flex items-center gap-3 animate-slide-in" style={{animationDelay: '0.2s'}}>
              <div className="text-sm text-muted-foreground">
                <span>Response time: </span>
                <span className="font-medium text-green-600">~24 hours</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">System Online</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-12 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-8">
            {/* Quick Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8 animate-fade-in" style={{animationDelay: '0.4s'}}>
              {[
                { label: "Reports This Week", value: "127", icon: TrendingUp, color: "text-blue-600" },
                { label: "Avg Response Time", value: "18h", icon: Timer, color: "text-green-600" },
                { label: "Resolution Rate", value: "94%", icon: Target, color: "text-purple-600" },
                { label: "Community Score", value: "4.8", icon: Award, color: "text-yellow-600" }
              ].map((stat, index) => (
                <Card key={index} className="hover:shadow-lg transition-all duration-200 hover:scale-105">
                  <CardContent className="p-4 text-center">
                    <stat.icon className={`h-6 w-6 ${stat.color} mx-auto mb-2`} />
                    <div className="font-bold text-lg font-playfair">{stat.value}</div>
                    <div className="text-xs text-muted-foreground">{stat.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Report Form */}
            <Card className="animate-fade-in" style={{animationDelay: '0.6s'}}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-500" />
                  Issue Details
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Please provide as much detail as possible to help us address your concern effectively
                </p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Issue Title */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Issue Title *</label>
                    <Input
                      placeholder="Brief, descriptive title of the issue..."
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      required
                      className="transition-all duration-200 focus:scale-[1.01]"
                    />
                  </div>

                  {/* Category Selection */}
                  <div>
                    <label className="block text-sm font-medium mb-3">Issue Category *</label>
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                      {issueCategories.map((category) => (
                        <div
                          key={category.value}
                          onClick={() => setFormData({...formData, category: category.value})}
                          className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-md ${
                            formData.category === category.value
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                          }`}
                        >
                          <category.icon className="h-6 w-6 mb-2 mx-auto text-muted-foreground" />
                          <div className="text-sm font-medium text-center">{category.label}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Priority Level */}
                  <div>
                    <label className="block text-sm font-medium mb-3">Priority Level *</label>
                    <div className="space-y-3">
                      {priorityLevels.map((priority) => (
                        <div
                          key={priority.value}
                          onClick={() => setFormData({...formData, priority: priority.value})}
                          className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:scale-[1.01] hover:shadow-md ${
                            formData.priority === priority.value
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                              : 'border-gray-200 dark:border-gray-700'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="font-medium">{priority.label}</div>
                              <div className="text-sm text-muted-foreground">{priority.description}</div>
                            </div>
                            <Flag className={`h-5 w-5 ${
                              priority.value === 'high' ? 'text-red-500' :
                              priority.value === 'medium' ? 'text-yellow-500' : 'text-green-500'
                            }`} />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Detailed Description *</label>
                    <Textarea
                      placeholder="Please describe the issue in detail, including when you first noticed it, how it affects you or others, and any other relevant information..."
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      required
                      rows={5}
                      className="transition-all duration-200 focus:scale-[1.01]"
                    />
                  </div>

                  {/* Location */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Location *</label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Street address, intersection, or landmark..."
                        value={formData.location}
                        onChange={(e) => setFormData({...formData, location: e.target.value})}
                        required
                        className="pl-10 transition-all duration-200 focus:scale-[1.01]"
                      />
                    </div>
                  </div>

                  {/* Image Upload */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Photos & Video (Optional)</label>
                    <div className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                      <Camera className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground mb-2">
                        Upload photos and video to help illustrate the issue (Max 3 photos + 1 video)
                      </p>
                      <input
                        type="file"
                        multiple
                        accept="image/*,video/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        id="image-upload"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          const uploadElement = document.getElementById('image-upload');
                          if (uploadElement) uploadElement.click();
                        }}
                        className="btn-hover"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Choose Photos & Video
                      </Button>
                      {selectedImages.length > 0 && (
                        <div className="mt-3 text-sm text-green-600">
                          {selectedImages.filter(f => f.type.startsWith('image/')).length} photo(s) and {selectedImages.filter(f => f.type.startsWith('video/')).length} video(s) selected
                        </div>
                      )}
                      <div className="mt-2 text-xs text-muted-foreground">
                        Supported formats: JPG, PNG, GIF for photos • MP4, MOV, AVI for videos (max 50MB each)
                      </div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <Card className="bg-gray-50 dark:bg-gray-900">
                    <CardHeader>
                      <CardTitle className="text-lg">Contact Information</CardTitle>
                      <p className="text-sm text-muted-foreground">
                        We may need to contact you for additional information or updates
                      </p>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium mb-2">Full Name *</label>
                          <div className="relative">
                            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                              placeholder="Your full name"
                              value={formData.contactName}
                              onChange={(e) => setFormData({...formData, contactName: e.target.value})}
                              required
                              className="pl-10"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-2">Email Address *</label>
                          <div className="relative">
                            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              value={formData.contactEmail}
                              onChange={(e) => setFormData({...formData, contactEmail: e.target.value})}
                              required
                              className="pl-10"
                            />
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-2">Phone Number (Optional)</label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="tel"
                            placeholder="(*************"
                            value={formData.contactPhone}
                            onChange={(e) => setFormData({...formData, contactPhone: e.target.value})}
                            className="pl-10"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Submit Button */}
                  <div className="flex flex-col sm:flex-row gap-4 pt-6">
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex-1 h-12 text-base font-medium btn-hover"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Submitting Report...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Submit Report
                        </>
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      className="sm:w-32 h-12 btn-hover"
                      onClick={() => {
                        setFormData({
                          title: '',
                          description: '',
                          category: '',
                          priority: '',
                          location: '',
                          contactName: '',
                          contactEmail: '',
                          contactPhone: '',
                          anonymous: false
                        });
                        setSelectedImages([]);
                      }}
                    >
                      Clear Form
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-4">
            <div className="space-y-6 sticky top-24">
              {/* Recent Reports */}
              <Card className="animate-slide-in" style={{animationDelay: '0.8s'}}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-green-500" />
                    Recent Community Reports
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {recentReports.map((report, index) => (
                    <div key={index} className="border-b border-gray-200 dark:border-gray-700 pb-3 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800/50 -mx-3 px-3 py-2 rounded transition-colors">
                      <h4 className="font-medium text-sm mb-1 hover:text-blue-600 cursor-pointer transition-colors">
                        {report.title}
                      </h4>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{report.category}</span>
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            report.status === 'resolved' ? 'border-green-300 text-green-700' :
                            report.status === 'in-progress' ? 'border-blue-300 text-blue-700' :
                            'border-orange-300 text-orange-700'
                          }`}
                        >
                          {report.status}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">{report.time}</div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Help & Guidelines */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <HelpCircle className="h-5 w-5 text-blue-500" />
                    Reporting Guidelines
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3 text-sm">
                    <div className="flex items-start gap-3">
                      <Lightbulb className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium">Be Specific</div>
                        <div className="text-muted-foreground">Provide exact locations and detailed descriptions</div>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <Camera className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium">Include Photos & Video</div>
                        <div className="text-muted-foreground">Visual evidence helps authorities understand the issue better</div>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <Clock className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium">Set Appropriate Priority</div>
                        <div className="text-muted-foreground">Help us prioritize urgent safety issues</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Support */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5 text-purple-500" />
                    Need Help?
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors cursor-pointer">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium text-sm">Support Hotline</div>
                      <div className="text-xs text-muted-foreground">(555) 311-HELP</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors cursor-pointer">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium text-sm">Email Support</div>
                      <div className="text-xs text-muted-foreground"><EMAIL></div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded transition-colors cursor-pointer">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium text-sm">Live Chat</div>
                      <div className="text-xs text-green-600">Available 24/7</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Downloadable Resources */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-5 w-5 text-indigo-500" />
                    Resources
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="ghost" className="w-full justify-start text-sm hover:scale-105 transition-transform">
                    <Download className="h-4 w-4 mr-2" />
                    Community Guidelines PDF
                  </Button>
                  <Button variant="ghost" className="w-full justify-start text-sm hover:scale-105 transition-transform">
                    <Download className="h-4 w-4 mr-2" />
                    Issue Categories Guide
                  </Button>
                  <Button variant="ghost" className="w-full justify-start text-sm hover:scale-105 transition-transform">
                    <Download className="h-4 w-4 mr-2" />
                    Photo & Video Guidelines
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Section */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16 pb-20 md:pb-0">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Related Pages */}
            <div>
              <h4 className="font-semibold mb-4">Related Pages</h4>
              <div className="space-y-2 text-sm">
                <a href="/" className="block text-muted-foreground hover:text-foreground transition-colors">View All Issues</a>
                <a href="/authority" className="block text-muted-foreground hover:text-foreground transition-colors">Authority Response</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Community Guidelines</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Track Your Reports</a>
              </div>
            </div>

            {/* Additional Resources */}
            <div>
              <h4 className="font-semibold mb-4">Resources</h4>
              <div className="space-y-2 text-sm">
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">How to Report</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Priority Guidelines</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">FAQ</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Contact Support</a>
              </div>
            </div>

            {/* Social Media Links */}
            <div>
              <h4 className="font-semibold mb-4">Connect</h4>
              <div className="space-y-2 text-sm">
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Twitter Updates</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Facebook Community</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Newsletter</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Mobile App</a>
              </div>
            </div>

            {/* Copyright Information */}
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>© 2024 Notice Board Platform</p>
                <p>All rights reserved</p>
                <a href="#" className="block hover:text-foreground transition-colors">Privacy Policy</a>
                <a href="#" className="block hover:text-foreground transition-colors">Terms of Service</a>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Mobile Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 md:hidden z-50">
        <div className="grid grid-cols-5 gap-1">
          <a
            href="/"
            className="flex flex-col items-center justify-center py-2 px-1 text-xs text-muted-foreground hover:text-foreground hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <Home className="h-5 w-5 mb-1" />
            <span>Home</span>
          </a>
          <a
            href="/report"
            className="flex flex-col items-center justify-center py-2 px-1 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400"
          >
            <AlertCircle className="h-5 w-5 mb-1" />
            <span>Report</span>
          </a>
          <a
            href="/authority"
            className="flex flex-col items-center justify-center py-2 px-1 text-xs text-muted-foreground hover:text-foreground hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <Shield className="h-5 w-5 mb-1" />
            <span>Authority</span>
          </a>
          <button
            className="flex flex-col items-center justify-center py-2 px-1 text-xs text-muted-foreground hover:text-foreground hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <BarChart3 className="h-5 w-5 mb-1" />
            <span>Stats</span>
          </button>
          <button
            className="flex flex-col items-center justify-center py-2 px-1 text-xs text-muted-foreground hover:text-foreground hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors relative"
          >
            <Bell className="h-5 w-5 mb-1" />
            <span>Alerts</span>
            <div className="absolute top-1 right-3 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
          </button>
        </div>
      </div>
    </div>
  );
}