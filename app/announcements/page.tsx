import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Megaphone, Building2, Users, FileText, Clock, Calendar } from 'lucide-react';

export default function AnnouncementsPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-black font-sans flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 md:py-12">
        <div className="max-w-5xl mx-auto">
          {/* Page Header */}
          <div className="mb-10 border-b border-gray-200 dark:border-gray-800 pb-8">
            <div className="flex items-center gap-3 mb-3">
              <Megaphone className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <h1 className="text-4xl md:text-5xl font-bold font-playfair">Announcements</h1>
            </div>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl">
              Official notices, upcoming events, and important information from local authorities and community organizations
            </p>
          </div>

          {/* Featured Announcement */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-xl overflow-hidden mb-10">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="p-6 md:p-8 flex flex-col justify-center">
                <Badge variant="outline" className="w-fit mb-4 bg-white dark:bg-black border-blue-200 dark:border-blue-700">
                  FEATURED ANNOUNCEMENT
                </Badge>
                <h2 className="text-2xl font-bold mb-4">Community Town Hall Meeting</h2>
                <p className="text-muted-foreground mb-6">
                  Join us for an important town hall meeting to discuss upcoming infrastructure projects, 
                  budget allocations, and new community initiatives. All residents are encouraged to attend 
                  and participate in the Q&A session.
                </p>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm">August 15, 2025</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm">6:00 PM - 8:00 PM</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm">City Hall, Main Auditorium</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm">Open to all residents</span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-3">
                  <Button className="bg-blue-600 hover:bg-blue-700">Add to Calendar</Button>
                  <Button variant="outline">Share Event</Button>
                </div>
              </div>
              <div className="relative h-64 md:h-auto">
                <img 
                  src="https://images.unsplash.com/photo-1552664730-d307ca884978" 
                  alt="Town Hall Meeting" 
                  className="w-full h-full object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 md:hidden">
                  <h3 className="text-white font-bold">Community Town Hall Meeting</h3>
                </div>
              </div>
            </div>
          </div>

          {/* Categorized Announcements */}
          <Tabs defaultValue="all" className="mb-10">
            <div className="flex justify-between items-center mb-6">
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="events">Events</TabsTrigger>
                <TabsTrigger value="notices">Notices</TabsTrigger>
                <TabsTrigger value="services">Services</TabsTrigger>
              </TabsList>
              <div>
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>Calendar View</span>
                </Button>
              </div>
            </div>

            <TabsContent value="all" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Announcement Card 1 */}
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <Badge variant="outline" className="bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300 border-green-200 dark:border-green-800">
                        Event
                      </Badge>
                      <span className="text-xs text-muted-foreground">Posted 3 days ago</span>
                    </div>
                    <CardTitle className="mt-3">Summer Festival in Central Park</CardTitle>
                    <CardDescription>
                      Join us for a weekend of music, food, and community activities
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-2 text-sm mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>Aug 20-22, 2025</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>10:00 AM - 8:00 PM</span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      The annual Summer Festival returns with live performances, 
                      local vendor booths, children's activities, and more. Free admission for all residents.
                    </p>
                  </CardContent>
                  <CardFooter className="border-t pt-4 flex justify-between">
                    <Button variant="outline" size="sm">View Details</Button>
                    <Button variant="ghost" size="sm">Share</Button>
                  </CardFooter>
                </Card>

                {/* Announcement Card 2 */}
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800">
                        Notice
                      </Badge>
                      <span className="text-xs text-muted-foreground">Posted 1 week ago</span>
                    </div>
                    <CardTitle className="mt-3">Water Conservation Advisory</CardTitle>
                    <CardDescription>
                      Voluntary water restrictions in effect due to drought conditions
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Due to ongoing drought conditions, the Water Department is asking all residents to 
                      voluntarily reduce water usage by 15%. This includes limiting lawn watering to twice 
                      per week and fixing any leaks promptly.
                    </p>
                  </CardContent>
                  <CardFooter className="border-t pt-4 flex justify-between">
                    <Button variant="outline" size="sm">View Details</Button>
                    <Button variant="ghost" size="sm">Share</Button>
                  </CardFooter>
                </Card>

                {/* Announcement Card 3 */}
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <Badge variant="outline" className="bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300 border-purple-200 dark:border-purple-800">
                        Services
                      </Badge>
                      <span className="text-xs text-muted-foreground">Posted 2 weeks ago</span>
                    </div>
                    <CardTitle className="mt-3">Free Health Screenings Available</CardTitle>
                    <CardDescription>
                      Community health center offers no-cost preventive screenings
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-2 text-sm mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>Every Tuesday & Thursday</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>9:00 AM - 4:00 PM</span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      The Community Health Center is offering free blood pressure, cholesterol, 
                      and diabetes screenings for all residents. No appointment necessary.
                    </p>
                  </CardContent>
                  <CardFooter className="border-t pt-4 flex justify-between">
                    <Button variant="outline" size="sm">View Details</Button>
                    <Button variant="ghost" size="sm">Share</Button>
                  </CardFooter>
                </Card>

                {/* Announcement Card 4 */}
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <Badge variant="outline" className="bg-orange-50 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300 border-orange-200 dark:border-orange-800">
                        Notice
                      </Badge>
                      <span className="text-xs text-muted-foreground">Posted 2 weeks ago</span>
                    </div>
                    <CardTitle className="mt-3">Road Construction Update</CardTitle>
                    <CardDescription>
                      Lane closures and detours on Main Street beginning next week
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-2 text-sm mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>Aug 10 - Sep 15, 2025</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span>Project #RC-2025-118</span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      The Public Works Department will begin resurfacing Main Street between 
                      Oak Avenue and Pine Street. Expect delays and follow posted detour routes.
                    </p>
                  </CardContent>
                  <CardFooter className="border-t pt-4 flex justify-between">
                    <Button variant="outline" size="sm">View Details</Button>
                    <Button variant="ghost" size="sm">Share</Button>
                  </CardFooter>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="events" className="mt-0">
              <p className="text-muted-foreground mb-4">Showing community events and gatherings</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Event content would go here - similar cards as above but filtered for events */}
              </div>
            </TabsContent>
            
            <TabsContent value="notices" className="mt-0">
              <p className="text-muted-foreground mb-4">Showing official notices and advisories</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Notices content would go here - similar cards as above but filtered for notices */}
              </div>
            </TabsContent>
            
            <TabsContent value="services" className="mt-0">
              <p className="text-muted-foreground mb-4">Showing community services and programs</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Services content would go here - similar cards as above but filtered for services */}
              </div>
            </TabsContent>
          </Tabs>

          {/* Subscription Box */}
          <div className="bg-gray-50 dark:bg-gray-900 border border-gray-100 dark:border-gray-800 rounded-xl p-6 text-center mt-10">
            <h2 className="text-2xl font-bold mb-4">Never Miss an Announcement</h2>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Subscribe to our community bulletin to receive weekly updates about announcements, 
              events, and important notices directly to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input 
                type="email" 
                placeholder="Your email address" 
                className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 flex-1"
              />
              <Button className="bg-blue-600 hover:bg-blue-700">Subscribe</Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
