import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Users, 
  Heart, 
  History, 
  Target, 
  Award, 
  BarChart, 
  Clock, 
  FileText, 
  Mail, 
  Building2 
} from 'lucide-react';

export default function AboutUsPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-black font-sans flex flex-col">
      <Header />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-b from-blue-50 to-white dark:from-blue-950/30 dark:to-black py-16 md:py-24">
          <div className="container mx-auto px-4 max-w-5xl">
            <div className="flex flex-col md:flex-row items-center gap-10">
              <div className="md:w-1/2">
                <h1 className="text-4xl md:text-5xl font-bold font-playfair mb-6">About The Notice Board</h1>
                <p className="text-lg text-muted-foreground mb-6">
                  Connecting communities with local authorities to solve problems together. Our platform empowers citizens 
                  to report issues, track progress, and stay informed about what's happening in their neighborhood.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button className="bg-blue-600 hover:bg-blue-700">Join Our Community</Button>
                  <Button variant="outline">Contact Us</Button>
                </div>
              </div>
              <div className="md:w-1/2">
                <div className="rounded-xl overflow-hidden shadow-xl">
                  <img 
                    src="https://images.unsplash.com/photo-1517048676732-d65bc937f952" 
                    alt="Team collaboration" 
                    className="w-full h-auto"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4 max-w-5xl">
            <div className="grid md:grid-cols-2 gap-10">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-8 rounded-xl border border-blue-100 dark:border-blue-800">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-10 w-10 bg-blue-600 rounded-full flex items-center justify-center">
                    <Heart className="h-5 w-5 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold">Our Mission</h2>
                </div>
                <p className="text-muted-foreground">
                  To bridge the gap between citizens and local authorities by providing an effective, 
                  transparent platform for community issue reporting and resolution tracking. We aim to 
                  foster collaboration, accountability, and civic engagement in every neighborhood.
                </p>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-8 rounded-xl border border-green-100 dark:border-green-800">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-10 w-10 bg-green-600 rounded-full flex items-center justify-center">
                    <Target className="h-5 w-5 text-white" />
                  </div>
                  <h2 className="text-2xl font-bold">Our Vision</h2>
                </div>
                <p className="text-muted-foreground">
                  We envision thriving communities where residents are empowered to shape their neighborhoods, 
                  where local governance is responsive and transparent, and where technology facilitates 
                  meaningful civic participation for all citizens regardless of background.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Our Story */}
        <section className="py-16 md:py-24 bg-gray-50 dark:bg-gray-900">
          <div className="container mx-auto px-4 max-w-5xl">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-playfair mb-4">Our Story</h2>
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                How we've grown from a simple idea to a community-driven platform
              </p>
            </div>

            <div className="relative">
              {/* Timeline line */}
              <div className="hidden md:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700 transform -translate-x-1/2"></div>
              
              <div className="space-y-12 relative">
                {/* Timeline Item 1 */}
                <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                  <div className="md:w-1/2 md:text-right">
                    <div className="bg-white dark:bg-black p-6 rounded-xl shadow-md">
                      <h3 className="text-xl font-bold mb-2">2022: The Beginning</h3>
                      <p className="text-muted-foreground">
                        Founded by a group of civic-minded technologists who saw the need for better 
                        communication between citizens and local authorities. The first prototype was 
                        tested in three neighborhoods.
                      </p>
                    </div>
                  </div>
                  <div className="hidden md:block relative z-10 h-10 w-10 rounded-full bg-blue-600 border-4 border-white dark:border-black flex items-center justify-center">
                    <History className="h-5 w-5 text-white" />
                  </div>
                  <div className="md:w-1/2"></div>
                </div>

                {/* Timeline Item 2 */}
                <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                  <div className="md:w-1/2"></div>
                  <div className="hidden md:block relative z-10 h-10 w-10 rounded-full bg-blue-600 border-4 border-white dark:border-black flex items-center justify-center">
                    <History className="h-5 w-5 text-white" />
                  </div>
                  <div className="md:w-1/2">
                    <div className="bg-white dark:bg-black p-6 rounded-xl shadow-md">
                      <h3 className="text-xl font-bold mb-2">2023: Growth & Expansion</h3>
                      <p className="text-muted-foreground">
                        Secured funding to expand our platform. Launched in 15 more communities and 
                        partnered with 5 municipal governments to integrate our system with city services.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Timeline Item 3 */}
                <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                  <div className="md:w-1/2 md:text-right">
                    <div className="bg-white dark:bg-black p-6 rounded-xl shadow-md">
                      <h3 className="text-xl font-bold mb-2">2024: Transformation</h3>
                      <p className="text-muted-foreground">
                        Redesigned our platform based on user feedback. Added real-time updates, 
                        enhanced analytics, and community features that connect neighbors.
                      </p>
                    </div>
                  </div>
                  <div className="hidden md:block relative z-10 h-10 w-10 rounded-full bg-blue-600 border-4 border-white dark:border-black flex items-center justify-center">
                    <History className="h-5 w-5 text-white" />
                  </div>
                  <div className="md:w-1/2"></div>
                </div>

                {/* Timeline Item 4 */}
                <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                  <div className="md:w-1/2"></div>
                  <div className="hidden md:block relative z-10 h-10 w-10 rounded-full bg-green-600 border-4 border-white dark:border-black flex items-center justify-center">
                    <History className="h-5 w-5 text-white" />
                  </div>
                  <div className="md:w-1/2">
                    <div className="bg-white dark:bg-black p-6 rounded-xl shadow-md">
                      <h3 className="text-xl font-bold mb-2">2025: Today & Beyond</h3>
                      <p className="text-muted-foreground">
                        Now serving over 100 communities nationwide with a 78% issue resolution rate. 
                        Continuing to innovate with AI-powered insights and predictive maintenance tools.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4 max-w-5xl">
            <div className="text-center mb-12">
              <div className="flex justify-center mb-4">
                <div className="h-10 w-10 bg-purple-600 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold font-playfair mb-4">Our Team</h2>
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                Meet the passionate individuals working to make our communities better
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Team Member Cards */}
              {[
                {
                  name: "Sarah Johnson",
                  role: "Founder & CEO",
                  image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2",
                  bio: "Former urban planner with a passion for civic technology and community engagement."
                },
                {
                  name: "David Chen",
                  role: "Chief Technology Officer",
                  image: "https://images.unsplash.com/photo-1560250097-0b93528c311a",
                  bio: "Software architect focused on building scalable platforms for social impact."
                },
                {
                  name: "Aisha Williams",
                  role: "Community Relations Director",
                  image: "https://images.unsplash.com/photo-1574034589502-9f8a1ed46fa7",
                  bio: "Community organizer with 10+ years experience in neighborhood advocacy."
                }
              ].map((member, index) => (
                <div key={index} className="bg-white dark:bg-gray-900 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                  <div className="aspect-[4/3] overflow-hidden">
                    <img 
                      src={member.image} 
                      alt={member.name} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="font-bold text-xl mb-1">{member.name}</h3>
                    <p className="text-blue-600 dark:text-blue-400 text-sm mb-4">{member.role}</p>
                    <p className="text-muted-foreground text-sm">{member.bio}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 md:py-24 bg-blue-600 text-white">
          <div className="container mx-auto px-4 max-w-5xl">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-playfair mb-4">Our Impact</h2>
              <p className="text-lg opacity-90 max-w-3xl mx-auto">
                Transforming communities through effective issue reporting and resolution
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {/* Stat Items */}
              {[
                { value: "100+", label: "Communities Served", icon: <Building2 className="h-8 w-8" /> },
                { value: "15,000+", label: "Issues Resolved", icon: <FileText className="h-8 w-8" /> },
                { value: "78%", label: "Resolution Rate", icon: <BarChart className="h-8 w-8" /> },
                { value: "3 Days", label: "Avg. Response Time", icon: <Clock className="h-8 w-8" /> }
              ].map((stat, index) => (
                <Card key={index} className="bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/20 transition-colors text-center">
                  <CardContent className="pt-6">
                    <div className="mb-4 flex justify-center">
                      {stat.icon}
                    </div>
                    <div className="text-3xl font-bold mb-1">{stat.value}</div>
                    <div className="text-sm opacity-80">{stat.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-16 md:py-24">
          <div className="container mx-auto px-4 max-w-5xl">
            <div className="grid md:grid-cols-2 gap-10">
              <div>
                <h2 className="text-3xl font-bold font-playfair mb-6">Get in Touch</h2>
                <p className="text-muted-foreground mb-8">
                  Have questions, feedback, or want to learn more about implementing the Notice Board in your community? 
                  We'd love to hear from you!
                </p>

                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center text-blue-600 dark:text-blue-400">
                      <Mail className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-medium">Email Us</h3>
                      <p className="text-muted-foreground"><EMAIL></p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center text-blue-600 dark:text-blue-400">
                      <Building2 className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-medium">Visit Our Office</h3>
                      <p className="text-muted-foreground">
                        123 Community Way<br />
                        Suite 400<br />
                        Cityville, ST 12345
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-900 p-6 rounded-xl">
                <h3 className="text-xl font-medium mb-4">Send us a message</h3>
                <form className="space-y-4">
                  <div className="grid sm:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium block mb-1">Name</label>
                      <input type="text" className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                    </div>
                    <div>
                      <label className="text-sm font-medium block mb-1">Email</label>
                      <input type="email" className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium block mb-1">Subject</label>
                    <input type="text" className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" />
                  </div>
                  <div>
                    <label className="text-sm font-medium block mb-1">Message</label>
                    <textarea rows={4} className="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                  </div>
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">Send Message</Button>
                </form>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
