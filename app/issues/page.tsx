import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Filter, MapPin, MessageSquare, ArrowUp, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle2, Clock } from 'lucide-react';
import Image from 'next/image';

export default function IssuesPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-black font-sans flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 md:py-12">
        <div className="max-w-5xl mx-auto">
          {/* Hero section */}
          <div className="mb-10 border-b border-gray-200 dark:border-gray-800 pb-8">
            <h1 className="text-4xl md:text-5xl font-bold font-playfair mb-4">Community Issues</h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl">
              Browse, filter and search through reported community issues. Get updates on resolution progress and contribute to discussions.
            </p>
          </div>

          {/* Search bar */}
          <div className="mb-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input 
                placeholder="Search issues by keyword, location or ID..." 
                className="pl-10 bg-white dark:bg-black border-2 focus-visible:ring-blue-500" 
              />
            </div>
          </div>

          {/* Filter and sort controls */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <div className="flex flex-wrap gap-2 items-center">
              <Button variant="ghost" size="sm" className="flex gap-2 items-center border border-gray-200 dark:border-gray-800">
                <Filter className="h-4 w-4" />
                <span>Filters</span>
              </Button>
              <div className="flex flex-wrap gap-2 ml-2">
                <button className="px-4 py-2 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-800/40 transition-colors flex items-center gap-1">
                  <span>All</span>
                  <Badge variant="secondary" className="ml-1">24</Badge>
                </button>
                <button className="px-4 py-2 flex items-center gap-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm font-medium hover:bg-blue-100 hover:text-blue-800 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 transition-colors">
                  <AlertTriangle className="h-3 w-3" />
                  <span>Open</span>
                  <Badge variant="outline" className="ml-1">12</Badge>
                </button>
                <button className="px-4 py-2 flex items-center gap-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm font-medium hover:bg-blue-100 hover:text-blue-800 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 transition-colors">
                  <Clock className="h-3 w-3" />
                  <span>In Progress</span>
                  <Badge variant="outline" className="ml-1">8</Badge>
                </button>
                <button className="px-4 py-2 flex items-center gap-1 bg-gray-100 dark:bg-gray-800 rounded-full text-sm font-medium hover:bg-blue-100 hover:text-blue-800 dark:hover:bg-blue-900/30 dark:hover:text-blue-300 transition-colors">
                  <CheckCircle2 className="h-3 w-3" />
                  <span>Resolved</span>
                  <Badge variant="outline" className="ml-1">4</Badge>
                </button>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Sort by:</span>
              <select className="bg-transparent border border-gray-200 dark:border-gray-700 rounded-lg text-sm p-2">
                <option>Most Recent</option>
                <option>Most Votes</option>
                <option>Priority</option>
                <option>Near Me</option>
              </select>
            </div>
          </div>

          {/* Report new issue button */}
          <div className="mb-8 flex justify-center">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white py-6 px-8 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all flex items-center gap-2 w-full md:w-auto">
              <AlertTriangle className="h-5 w-5" />
              Report New Issue
            </Button>
          </div>

          {/* Issues grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Issue Card 1 */}
            <div className="border border-gray-200 dark:border-gray-800 rounded-xl overflow-hidden hover:shadow-lg transition-shadow group">
              <div className="relative">
                <div className="aspect-video bg-gray-100 dark:bg-gray-800 overflow-hidden relative">
                  <Image 
                    src="https://images.unsplash.com/photo-1625047509287-bf127652252b" 
                    alt="Pothole on Main Street"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="absolute top-3 left-3">
                  <div className="flex gap-2">
                    <Badge className="bg-amber-500 hover:bg-amber-600 text-white flex items-center gap-1">
                      <Clock className="h-3 w-3" /> In Progress
                    </Badge>
                    <Badge className="bg-red-500 hover:bg-red-600 text-white">High Priority</Badge>
                  </div>
                </div>
                <div className="absolute top-3 right-3">
                  <div className="flex items-center bg-black/60 text-white rounded-full px-3 py-1 text-xs">
                    <MapPin className="h-3 w-3 mr-1" /> Main Street
                  </div>
                </div>
              </div>
              <div className="p-5">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs font-mono bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded-md">#IN-7234</span>
                  <span className="text-xs text-muted-foreground">Updated 2 days ago</span>
                </div>
                <h3 className="font-bold text-lg mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors cursor-pointer">
                  Large Pothole on Main Street
                </h3>
                <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                  Deep pothole causing damage to vehicles and creating a hazard for cyclists. Located near the intersection of Main St and Oak Ave.
                </p>
                <div className="flex items-center justify-between border-t border-gray-100 dark:border-gray-800 pt-3">
                  <div className="flex items-center gap-4">
                    <button className="flex items-center gap-1 text-sm text-muted-foreground hover:text-blue-600 transition-colors">
                      <ArrowUp className="h-4 w-4" />
                      <span>32</span>
                    </button>
                    <button className="flex items-center gap-1 text-sm text-muted-foreground hover:text-blue-600 transition-colors">
                      <MessageSquare className="h-4 w-4" />
                      <span>14</span>
                    </button>
                  </div>
                  <Button variant="outline" size="sm">View Details</Button>
                </div>
              </div>
            </div>

            {/* Issue Card 2 */}
            <div className="border border-gray-200 dark:border-gray-800 rounded-xl overflow-hidden hover:shadow-lg transition-shadow group">
              <div className="relative">
                <div className="aspect-video bg-gray-100 dark:bg-gray-800 overflow-hidden relative">
                  <Image 
                    src="https://images.unsplash.com/photo-1572501322269-4fce4024010f" 
                    alt="Street Light Outage" 
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="absolute top-3 right-3">
                  <span className="px-2 py-1 bg-orange-100 text-orange-800 dark:bg-orange-900/60 dark:text-orange-200 text-xs font-medium rounded-full">
                    Medium Priority
                  </span>
                </div>
              </div>
              <div className="p-5">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200 px-2 py-1 rounded-full">
                    Open
                  </span>
                  <span className="text-xs text-muted-foreground">5 days ago</span>
                </div>
                <h3 className="font-bold text-lg mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  Street Light Outage on Elm Street
                </h3>
                <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                  Three consecutive street lights are not working on Elm Street between 5th and 6th Avenue, creating unsafe walking conditions at night.
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full bg-purple-600 flex items-center justify-center text-white text-xs">
                      AS
                    </div>
                    <span className="text-xs text-muted-foreground">Alice Smith</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-muted-foreground">18 votes</span>
                    <span className="text-xs text-muted-foreground">•</span>
                    <span className="text-xs text-muted-foreground">4 comments</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Issue Card 3 */}
            <div className="border border-gray-200 dark:border-gray-800 rounded-xl overflow-hidden hover:shadow-lg transition-shadow group">
              <div className="relative">
                <div className="aspect-video bg-gray-100 dark:bg-gray-800 overflow-hidden relative">
                  <Image 
                    src="https://images.unsplash.com/photo-1599486130156-a41caef06805" 
                    alt="Playground Equipment Damage" 
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="absolute top-3 right-3">
                  <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/60 dark:text-green-200 text-xs font-medium rounded-full">
                    Resolved
                  </span>
                </div>
              </div>
              <div className="p-5">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200 px-2 py-1 rounded-full">
                    Completed
                  </span>
                  <span className="text-xs text-muted-foreground">2 weeks ago</span>
                </div>
                <h3 className="font-bold text-lg mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  Damaged Playground Equipment
                </h3>
                <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                  The swing set at Central Park playground has broken chains creating a safety hazard for children. Needs urgent repair.
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full bg-green-600 flex items-center justify-center text-white text-xs">
                      RJ
                    </div>
                    <span className="text-xs text-muted-foreground">Robert Johnson</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-muted-foreground">45 votes</span>
                    <span className="text-xs text-muted-foreground">•</span>
                    <span className="text-xs text-muted-foreground">12 comments</span>
                  </div>
                </div>
              </div>
            </div>

            {/* More issue cards would go here */}
          </div>

          {/* Pagination */}
          <div className="flex justify-center mt-10">
            <nav className="inline-flex rounded-md shadow" aria-label="Pagination">
              <a href="#" className="px-3 py-2 rounded-l-md border border-gray-200 dark:border-gray-700 text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-800">
                Previous
              </a>
              <a href="#" className="px-3 py-2 border-t border-b border-gray-200 dark:border-gray-700 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20">
                1
              </a>
              <a href="#" className="px-3 py-2 border-t border-b border-gray-200 dark:border-gray-700 text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-800">
                2
              </a>
              <a href="#" className="px-3 py-2 border-t border-b border-gray-200 dark:border-gray-700 text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-800">
                3
              </a>
              <span className="px-3 py-2 border-t border-b border-gray-200 dark:border-gray-700 text-sm text-gray-500">...</span>
              <a href="#" className="px-3 py-2 border-t border-b border-gray-200 dark:border-gray-700 text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-800">
                8
              </a>
              <a href="#" className="px-3 py-2 rounded-r-md border border-gray-200 dark:border-gray-700 text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-800">
                Next
              </a>
            </nav>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
