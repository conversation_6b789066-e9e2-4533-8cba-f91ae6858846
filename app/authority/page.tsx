"use client";

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  Clock, 
  Search, 
  Filter,
  Download,
  Bell,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Timer,
  MapPin,
  Calendar,
  FileText,
  Phone,
  Mail,
  MessageSquare,
  BarChart3,
  Settings,
  ChevronRight,
  AlertCircle,
  Home,
  Eye,
  Edit,
  Archive,
  Flag,
  User,
  Building,
  Zap,
  Target,
  Activity
} from 'lucide-react';

const mockReports = [
  {
    id: "RPT-2024-001",
    title: "Street Light Outage - Main Street Intersection",
    description: "Multiple street lights are non-functional creating safety hazards for pedestrians and drivers during evening hours.",
    location: "Main Street & 5th Avenue",
    category: "Infrastructure",
    priority: "high",
    status: "pending",
    submittedBy: "<PERSON> Johnson",
    submittedDate: "2024-01-15T10:30:00Z",
    assignedTo: "Public Works Dept",
    estimatedResolution: "2024-01-18",
    communitySupport: 23,
    comments: 7,
    images: 3,
    urgencyScore: 85
  },
  {
    id: "RPT-2024-002",
    title: "Pothole Damage on Park Avenue",
    description: "Large pothole causing vehicle damage and creating traffic hazards near the Central Park entrance.",
    location: "Park Avenue near Central Park",
    category: "Roads",
    priority: "medium",
    status: "in-progress",
    submittedBy: "Mike Chen",
    submittedDate: "2024-01-14T14:20:00Z",
    assignedTo: "Road Maintenance",
    estimatedResolution: "2024-01-20",
    communitySupport: 18,
    comments: 4,
    images: 2,
    urgencyScore: 65
  },
  {
    id: "RPT-2024-003",
    title: "Noise Violation - Construction Site",
    description: "Construction work starting before permitted hours, violating city noise ordinances and disturbing residents.",
    location: "Residential Block 15",
    category: "Noise",
    priority: "medium",
    status: "pending",
    submittedBy: "David Brown",
    submittedDate: "2024-01-15T07:45:00Z",
    assignedTo: "Code Enforcement",
    estimatedResolution: "2024-01-17",
    communitySupport: 31,
    comments: 12,
    images: 1,
    urgencyScore: 70
  }
];

const departmentStats = [
  { department: "Public Works", pending: 12, inProgress: 8, resolved: 45, avgResponseTime: "18 hours" },
  { department: "Road Maintenance", pending: 6, inProgress: 15, resolved: 32, avgResponseTime: "24 hours" },
  { department: "Code Enforcement", pending: 9, inProgress: 4, resolved: 28, avgResponseTime: "12 hours" },
  { department: "Parks & Recreation", pending: 3, inProgress: 7, resolved: 19, avgResponseTime: "36 hours" }
];

export default function AuthorityResponseCenter() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [selectedReport, setSelectedReport] = useState(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-300';
      case 'in-progress': return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300';
      case 'resolved': return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300';
      default: return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300';
      case 'medium': return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'low': return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300';
      default: return 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Timer className="h-3 w-3" />;
      case 'in-progress': return <AlertTriangle className="h-3 w-3" />;
      case 'resolved': return <CheckCircle className="h-3 w-3" />;
      default: return <Timer className="h-3 w-3" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 pb-20 md:pb-0">
      {/* Header Section */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          {/* Breadcrumb Navigation */}
          <nav className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
            <Home className="h-4 w-4" />
            <ChevronRight className="h-3 w-3" />
            <span>Authority Portal</span>
            <ChevronRight className="h-3 w-3" />
            <span className="text-foreground font-medium">Response Center</span>
          </nav>

          {/* Page Title and Actions */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h1 className="font-playfair text-3xl font-bold">Authority Response Center</h1>
              </div>
              <p className="text-muted-foreground">Manage and respond to community reports efficiently</p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="text-sm text-muted-foreground">
                <span>Last updated: </span>
                <span className="font-medium">2 minutes ago</span>
                <div className="inline-block w-2 h-2 bg-green-500 rounded-full ml-2 animate-pulse"></div>
              </div>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Reports
              </Button>
              <Button size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-12 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-8">
            {/* Quick Stats Dashboard */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {[
                { label: "Pending Review", value: "47", icon: Timer, color: "text-orange-600", bg: "bg-orange-100 dark:bg-orange-900/20" },
                { label: "In Progress", value: "34", icon: Activity, color: "text-blue-600", bg: "bg-blue-100 dark:bg-blue-900/20" },
                { label: "Resolved Today", value: "23", icon: CheckCircle, color: "text-green-600", bg: "bg-green-100 dark:bg-green-900/20" },
                { label: "High Priority", value: "12", icon: Flag, color: "text-red-600", bg: "bg-red-100 dark:bg-red-900/20" }
              ].map((stat, index) => (
                <Card key={index} className="hover:shadow-lg transition-all duration-200 hover:scale-105">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground">{stat.label}</p>
                        <p className="text-2xl font-bold font-playfair">{stat.value}</p>
                      </div>
                      <div className={`p-2 rounded-lg ${stat.bg}`}>
                        <stat.icon className={`h-5 w-5 ${stat.color}`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Filters and Search */}
            <Card className="mb-8">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search reports by ID, title, location, or submitter..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="resolved">Resolved</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Priority</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Department" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Departments</SelectItem>
                        <SelectItem value="public-works">Public Works</SelectItem>
                        <SelectItem value="road-maintenance">Road Maintenance</SelectItem>
                        <SelectItem value="code-enforcement">Code Enforcement</SelectItem>
                        <SelectItem value="parks">Parks & Recreation</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Reports List */}
            <div className="space-y-6">
              {mockReports.map((report, index) => (
                <Card key={report.id} className="hover:shadow-lg transition-all duration-200 hover:scale-[1.01]">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-start gap-6">
                      {/* Report Details */}
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <Badge variant="outline" className="font-mono text-xs">
                            {report.id}
                          </Badge>
                          <Badge className={`${getStatusColor(report.status)} flex items-center gap-1`} variant="outline">
                            {getStatusIcon(report.status)}
                            {report.status.replace('-', ' ').toUpperCase()}
                          </Badge>
                          <Badge className={`${getPriorityColor(report.priority)}`} variant="outline">
                            {report.priority.toUpperCase()}
                          </Badge>
                          {report.urgencyScore > 80 && (
                            <Badge className="bg-red-500 text-white animate-pulse">
                              URGENT
                            </Badge>
                          )}
                        </div>
                        
                        <h3 className="font-playfair text-xl font-bold mb-2 hover:text-blue-600 cursor-pointer transition-colors">
                          {report.title}
                        </h3>
                        
                        <p className="text-muted-foreground mb-4 leading-relaxed">
                          {report.description}
                        </p>
                        
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span>{report.location}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span>{report.submittedBy}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <span>{report.assignedTo}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span>Due: {new Date(report.estimatedResolution).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                      
                      {/* Action Panel */}
                      <div className="lg:w-64">
                        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                          <div className="grid grid-cols-3 gap-2 mb-4 text-center">
                            <div>
                              <div className="text-lg font-bold text-blue-600">{report.communitySupport}</div>
                              <div className="text-xs text-muted-foreground">Supports</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-green-600">{report.comments}</div>
                              <div className="text-xs text-muted-foreground">Comments</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-purple-600">{report.images}</div>
                              <div className="text-xs text-muted-foreground">Images</div>
                            </div>
                          </div>
                          
                          <div className="space-y-2">
                            <Button size="sm" className="w-full">
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Button>
                            <Button variant="outline" size="sm" className="w-full">
                              <Edit className="h-4 w-4 mr-2" />
                              Update Status
                            </Button>
                            <Button variant="outline" size="sm" className="w-full">
                              <MessageSquare className="h-4 w-4 mr-2" />
                              Respond
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-4">
            <div className="space-y-6 sticky top-24">
              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-500" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <Bell className="h-4 w-4 mr-2" />
                    Send Bulk Notifications
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Generate Reports
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Archive className="h-4 w-4 mr-2" />
                    Archive Resolved
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Target className="h-4 w-4 mr-2" />
                    Set Priorities
                  </Button>
                </CardContent>
              </Card>

              {/* Department Performance */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-500" />
                    Department Performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {departmentStats.map((dept, index) => (
                      <div key={index} className="border-b border-gray-200 dark:border-gray-700 pb-3 last:border-b-0">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">{dept.department}</span>
                          <span className="text-xs text-muted-foreground">Avg: {dept.avgResponseTime}</span>
                        </div>
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div className="text-center">
                            <div className="font-bold text-orange-600">{dept.pending}</div>
                            <div className="text-muted-foreground">Pending</div>
                          </div>
                          <div className="text-center">
                            <div className="font-bold text-blue-600">{dept.inProgress}</div>
                            <div className="text-muted-foreground">Active</div>
                          </div>
                          <div className="text-center">
                            <div className="font-bold text-green-600">{dept.resolved}</div>
                            <div className="text-muted-foreground">Resolved</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Phone className="h-5 w-5 text-blue-500" />
                    Emergency Contacts
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium text-sm">Emergency Dispatch</div>
                      <div className="text-xs text-muted-foreground">(555) 911-HELP</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium text-sm">Authority Support</div>
                      <div className="text-xs text-muted-foreground"><EMAIL></div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium text-sm">Live Chat</div>
                      <div className="text-xs text-green-600">Available 24/7</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Downloadable Resources */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-purple-500" />
                    Resources
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="ghost" className="w-full justify-start text-sm">
                    <Download className="h-4 w-4 mr-2" />
                    Response Guidelines PDF
                  </Button>
                  <Button variant="ghost" className="w-full justify-start text-sm">
                    <Download className="h-4 w-4 mr-2" />
                    Monthly Report Template
                  </Button>
                  <Button variant="ghost" className="w-full justify-start text-sm">
                    <Download className="h-4 w-4 mr-2" />
                    Priority Classification Guide
                  </Button>
                  <Button variant="ghost" className="w-full justify-start text-sm">
                    <Download className="h-4 w-4 mr-2" />
                    Community Engagement Best Practices
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Section */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Related Pages */}
            <div>
              <h4 className="font-semibold mb-4">Related Pages</h4>
              <div className="space-y-2 text-sm">
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Analytics Dashboard</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">User Management</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">System Settings</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">API Documentation</a>
              </div>
            </div>

            {/* Additional Resources */}
            <div>
              <h4 className="font-semibold mb-4">Resources</h4>
              <div className="space-y-2 text-sm">
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Training Materials</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Policy Guidelines</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">FAQ</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Contact Support</a>
              </div>
            </div>

            {/* Social Media Links */}
            <div>
              <h4 className="font-semibold mb-4">Connect</h4>
              <div className="space-y-2 text-sm">
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Official Twitter</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">LinkedIn</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Government Portal</a>
                <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">News Updates</a>
              </div>
            </div>

            {/* Copyright Information */}
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>© 2024 Municipal Authority</p>
                <p>All rights reserved</p>
                <a href="#" className="block hover:text-foreground transition-colors">Privacy Policy</a>
                <a href="#" className="block hover:text-foreground transition-colors">Terms of Service</a>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Mobile Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 md:hidden z-50">
        <div className="grid grid-cols-5 gap-1">
          <a
            href="/"
            className="flex flex-col items-center justify-center py-2 px-1 text-xs text-muted-foreground hover:text-foreground hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <Home className="h-5 w-5 mb-1" />
            <span>Home</span>
          </a>
          <a
            href="/report"
            className="flex flex-col items-center justify-center py-2 px-1 text-xs text-muted-foreground hover:text-foreground hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <AlertCircle className="h-5 w-5 mb-1" />
            <span>Report</span>
          </a>
          <a
            href="/authority"
            className="flex flex-col items-center justify-center py-2 px-1 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400"
          >
            <Shield className="h-5 w-5 mb-1" />
            <span>Authority</span>
          </a>
          <button
            className="flex flex-col items-center justify-center py-2 px-1 text-xs text-muted-foreground hover:text-foreground hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            <BarChart3 className="h-5 w-5 mb-1" />
            <span>Stats</span>
          </button>
          <button
            className="flex flex-col items-center justify-center py-2 px-1 text-xs text-muted-foreground hover:text-foreground hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors relative"
          >
            <Bell className="h-5 w-5 mb-1" />
            <span>Alerts</span>
            <div className="absolute top-1 right-3 w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
          </button>
        </div>
      </div>
    </div>
  );
}