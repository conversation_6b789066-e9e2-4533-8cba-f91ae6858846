import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Bell, Clock, AlertTriangle, User, MessageCircle, Share2, Radio, Newspaper, Facebook, Twitter, Linkedin, Mail } from 'lucide-react';

export default function BreakingNewsPage() {
  return (
    <div className="min-h-screen bg-white dark:bg-black font-sans flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 md:py-12">
        <div className="max-w-5xl mx-auto">
          {/* Breaking News Alert Banner */}
          <div className="bg-red-100 dark:bg-red-900/30 border-2 border-red-300 dark:border-red-800 rounded-xl p-5 mb-10">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center gap-3">
                <div className="bg-red-600 p-2 rounded-full animate-pulse">
                  <Radio className="h-5 w-5 text-white animate-pulse" />
                </div>
                <div>
                  <h2 className="font-bold text-red-800 dark:text-red-200 text-lg">LIVE BREAKING NEWS ALERTS</h2>
                  <p className="text-sm text-red-700 dark:text-red-300">Get instant notifications for critical community updates</p>
                </div>
              </div>
              <div className="w-full sm:w-auto flex gap-2">
                <div className="relative flex-grow">
                  <Input 
                    type="email" 
                    placeholder="Your email address" 
                    className="pr-24 border-red-200 dark:border-red-900 focus-visible:ring-red-500"
                  />
                  <Button size="sm" className="absolute right-0.5 top-0.5 bottom-0.5 bg-red-600 hover:bg-red-700 text-white hover:text-white">
                    <Bell className="h-4 w-4 mr-1" /> Subscribe
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Page Header */}
          <div className="mb-10 border-b border-gray-200 dark:border-gray-800 pb-8">
            <Badge variant="outline" className="mb-3 text-red-600 dark:text-red-400 border-red-200 dark:border-red-800">
              LIVE UPDATES
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold font-playfair mb-4">Breaking News</h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl">
              Stay informed with the latest urgent updates and critical developments in your community
            </p>
          </div>

          {/* Breaking News Articles */}
          <div className="space-y-8 mb-12">
            {/* Featured breaking news */}
            <div className="border border-red-200 dark:border-red-900 rounded-xl overflow-hidden bg-red-50 dark:bg-red-900/10">
              <div className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <span className="bg-red-600 rounded-full h-2 w-2 animate-pulse"></span>
                  <span className="text-red-600 dark:text-red-400 font-medium text-sm">LIVE NOW</span>
                  <span className="text-sm text-muted-foreground">•</span>
                  <span className="flex items-center text-sm text-muted-foreground gap-1">
                    <Clock className="h-3 w-3" /> 18 minutes ago
                  </span>
                </div>

                <h2 className="text-2xl font-bold mb-4">Flash Flood Warning Issued for Downtown Area</h2>
                
                <div className="prose dark:prose-invert max-w-none mb-6">
                  <p>
                    The National Weather Service has issued a flash flood warning for downtown and surrounding areas 
                    following intense rainfall. Residents are advised to stay away from low-lying areas and monitor 
                    official channels for updates.
                  </p>
                  <p>
                    Emergency services are currently responding to several calls for assistance. The city's 
                    emergency operations center has been activated, and temporary shelters are being set up at 
                    the following locations:
                  </p>
                  <ul>
                    <li>Central Community Center</li>
                    <li>North Side High School</li>
                    <li>East End Recreation Complex</li>
                  </ul>
                  <p>
                    Please check on elderly neighbors and those who may need assistance. Avoid driving through 
                    flooded roadways.
                  </p>
                </div>

                <div className="border border-red-200 dark:border-red-900 rounded-lg p-4 bg-white dark:bg-black mb-6">
                  <h3 className="font-medium mb-2">Emergency Contact Information:</h3>
                  <p className="text-sm">Flood Response Hotline: 555-123-4567</p>
                  <p className="text-sm">Emergency Services: 911</p>
                  <p className="text-sm">Road Conditions: 555-987-6543</p>
                </div>

                <div className="flex flex-wrap items-center gap-3">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <MessageCircle className="h-4 w-4" /> Comments (23)
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Share2 className="h-4 w-4" /> Share Update
                  </Button>
                  <Button size="sm" className="ml-auto bg-red-600 hover:bg-red-700 text-white">
                    View Full Coverage
                  </Button>
                </div>
              </div>
            </div>

            {/* Other breaking news items */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Breaking News Item 1 */}
              <div className="border border-gray-200 dark:border-gray-800 rounded-xl overflow-hidden hover:shadow-lg transition-shadow group">
                <div className="relative">
                  <div className="aspect-video bg-gray-100 dark:bg-gray-800 overflow-hidden">
                    <img 
                      src="https://images.unsplash.com/photo-1599586120429-48281b6f0ece" 
                      alt="Road closure"
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="absolute top-3 right-3">
                    <span className="px-2 py-1 bg-orange-100 text-orange-800 dark:bg-orange-900/60 dark:text-orange-200 text-xs font-medium rounded-full">
                      30 minutes ago
                    </span>
                  </div>
                </div>
                <div className="p-5">
                  <h3 className="font-bold text-lg mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    Major Traffic Accident on Highway 101 Causing Significant Delays
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    A multi-vehicle collision has resulted in all northbound lanes being closed. Authorities 
                    estimate clearance time of 2-3 hours. Commuters are advised to use alternate routes.
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center text-white text-xs">
                        TR
                      </div>
                      <span className="text-xs text-muted-foreground">Traffic Reporter</span>
                    </div>
                    <Button variant="ghost" size="sm">Read More</Button>
                  </div>
                </div>
              </div>

              {/* Breaking News Item 2 */}
              <div className="border border-gray-200 dark:border-gray-800 rounded-xl overflow-hidden hover:shadow-lg transition-shadow group">
                <div className="relative">
                  <div className="aspect-video bg-gray-100 dark:bg-gray-800 overflow-hidden">
                    <img 
                      src="https://images.unsplash.com/photo-1593113598332-cd288d649433" 
                      alt="Water main break"
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  <div className="absolute top-3 right-3">
                    <span className="px-2 py-1 bg-orange-100 text-orange-800 dark:bg-orange-900/60 dark:text-orange-200 text-xs font-medium rounded-full">
                      1 hour ago
                    </span>
                  </div>
                </div>
                <div className="p-5">
                  <h3 className="font-bold text-lg mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    Water Main Break Affects Service in Eastern Neighborhoods
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Residents in Cedar Heights and Pinewood areas may experience low water pressure or service 
                    disruptions. Repair crews are on-site and estimate restoration within 6 hours.
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full bg-green-600 flex items-center justify-center text-white text-xs">
                        WD
                      </div>
                      <span className="text-xs text-muted-foreground">Water Department</span>
                    </div>
                    <Button variant="ghost" size="sm">Read More</Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Updates Section */}
          <div className="border-t border-gray-200 dark:border-gray-800 pt-8 mb-10">
            <h2 className="text-2xl font-bold mb-6">Recent Updates</h2>
            <div className="space-y-6">
              {/* Update item with timeline design */}
              {[
                {
                  time: "2:45 PM",
                  title: "School Board Emergency Meeting",
                  content: "The school board has called an emergency meeting for 7:00 PM tonight to discuss potential school closures due to the weather conditions."
                },
                {
                  time: "1:30 PM",
                  title: "Power Outages Reported",
                  content: "Several neighborhoods in the western district are experiencing power outages due to downed power lines. Utility crews have been dispatched."
                },
                {
                  time: "12:15 PM",
                  title: "Community Center Opens as Cooling Station",
                  content: "With temperatures exceeding 100°F, the downtown community center has been designated as a cooling station for residents without air conditioning."
                }
              ].map((update, index) => (
                <div key={index} className="flex gap-4">
                  <div className="flex flex-col items-center">
                    <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-10 w-10 flex items-center justify-center text-sm font-medium">
                      {update.time}
                    </div>
                    {index < 2 && <div className="h-full w-0.5 bg-gray-200 dark:bg-gray-700 my-2"></div>}
                  </div>
                  <div className="flex-1 bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
                    <h3 className="font-medium mb-2">{update.title}</h3>
                    <p className="text-sm text-muted-foreground">{update.content}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Subscribe to alerts */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-xl p-6 text-center">
            <h2 className="text-2xl font-bold mb-4">Stay Informed in Real-Time</h2>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              Subscribe to our emergency alert system to receive instant notifications about critical events 
              in your community directly to your phone or email.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <input 
                type="email" 
                placeholder="Your email address" 
                className="px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Button className="bg-blue-600 hover:bg-blue-700">Subscribe to Alerts</Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
